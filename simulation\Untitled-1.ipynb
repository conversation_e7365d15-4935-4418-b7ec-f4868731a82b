{"cells": [{"cell_type": "code", "execution_count": 12, "id": "964ddd16", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173: UserWarning: bend_euler angle should be 90 or 180. Got 10.0. Use bend_euler_all_angle instead.\n", "  v = func(*args, **kwargs)\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                         </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, -0.9460000000000001) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -52.25)                │ optical   │\n", "│ o3   │ 1.5   │ 270.0       │ WG (1/0) │ (50.0, -52.25)                 │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, -0.9460000000000001)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                        \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, -0.9460000000000001) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -52.25)                │ optical   │\n", "│ o3   │ 1.5   │ 270.0       │ WG (1/0) │ (50.0, -52.25)                 │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, -0.9460000000000001)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴────────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "section_inner = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.coupler_ring_bend(\n", "    coupler_gap=0.5,\n", "    radius=50,\n", "    coupling_angle_coverage=20,\n", "    length_x=0,\n", "    cross_section_inner=section_inner,\n", "    cross_section_outer=section_outer,\n", "    bend_output=\"bend_euler\",\n", "    straight=\"straight\",\n", ")\n", "\n", "# 创建新的组件\n", "c = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = c << coupler\n", "\n", "# 添加两个直波导\n", "s1 = c << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "s2 = c << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o4\"])\n", "\n", "# 添加端口（新的端口位置）\n", "c.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "c.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "c.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "c.add_port(\"o4\", port=s2.ports[\"o2\"])\n", "\n", "c.pprint_ports()\n", "c.draw_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 54, "id": "a772169c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["端口信息:\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "│ o1   │ 2.0   │ 180.0       │ WG (1/0) │ (-30.809, 24.679000000000002) │ optical   │\n", "│ o2   │ 1.5   │ 270.0       │ WG (1/0) │ (-50.0, -26.625)              │ optical   │\n", "│ o3   │ 1.5   │ 90.0        │ WG (1/0) │ (50.0, -26.625)               │ optical   │\n", "│ o4   │ 2.0   │ 0.0         │ WG (1/0) │ (30.809, 24.679000000000002)  │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c = gf.Component()\n", "    # 这里是波导（1，0）\n", "    #WG = gf.components.bends.bend_euler_s(layer=(1,0))\n", "    #WG=bend_s_offset(offset=offset, radius=radius)\n", "    #WG=gf.components.crossing_etched(width=0.8, r1=r1, r2=r2, w=w, L=3.5, layer_wg='WG')\n", "    #xs = gf.cross_section.strip(width=1, layer=(1, 0))\n", "    # WG=gf.components.straight(\n", "    # length=10,  # Length in microns\n", "    # cross_section=xs\n", "    # )       \n", "section_inner = gf.cross_section.cross_section(\n", "width=1.5,\n", "offset=0,\n", "layer=(1, 0)\n", ")\n", "section_outer = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "\n", "# 创建基础环形耦合器\n", "coupler = gf.components.coupler_ring_bend(\n", "    coupler_gap=0.5,\n", "    radius=50,\n", "    coupling_angle_coverage=20,\n", "    length_x=0,\n", "    cross_section_inner=section_inner,\n", "    cross_section_outer=section_outer,\n", "    bend_output=\"bend_euler\",\n", "    straight=\"straight\",\n", ")\n", "\n", "# 创建新的组件\n", "WG = gf.Component()\n", "\n", "# 添加耦合器\n", "coupler_ref = WG << coupler\n", "\n", "# 添加两个直波导\n", "s1 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "s2 = WG << gf.get_component(\"straight\", length=20, cross_section=section_outer)\n", "\n", "# 连接直波导到耦合器的端口\n", "s1.connect(\"o1\", coupler_ref.ports[\"o1\"])\n", "s2.connect(\"o1\", coupler_ref.ports[\"o4\"])\n", "\n", "# 添加端口（新的端口位置）\n", "WG.add_port(\"o1\", port=s1.ports[\"o2\"])\n", "# WG.add_port(\"o2\", port=coupler_ref.ports[\"o2\"])\n", "\n", "WG.add_port(\"o3\", port=coupler_ref.ports[\"o3\"])\n", "# WG.add_port(\"o4\", port=s2.ports[\"o2\"])\n", "# Add the MMI component as a reference to our main component `c`.\n", "WG_ref = c.add_ref(WG)\n", "# Create a rectangle background with the same size as the MMI.\n", "# The .size attribute is a convenient way to get the (width, height) tuple.\n", "box = gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(3,0)\n", ")\n", "slab= gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(2,0)\n", ")\n", "clad=gf.components.rectangle(\n", "    size=(WG_ref.xmax - WG_ref.xmin+3, WG_ref.ymax - WG_ref.ymin+6), layer=(5,0)\n", ")\n", "# Add the background rectangle as a reference to `c`.\n", "rect_ref = c.add_ref(box)\n", "slab_ref = c.add_ref(slab) \n", "clad_ref = c.add_ref(clad)\n", "# Align the center of the rectangle with the center of the MMI.\n", "WG_ref.center = (0, 0)\n", "rect_ref.center = WG_ref.center\n", "slab_ref.center = WG_ref.center\n", "clad_ref.center = WG_ref.center\n", "# Add the optical ports from the MMI reference to the main component `c`.\n", "c.add_ports(WG_ref.ports)\n", "c.plot()\n", "c.draw_ports()"]}, {"cell_type": "code", "execution_count": 34, "id": "e628e076", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "# define a custom cross-section\n", "custom_in = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "custom_out = gf.cross_section.cross_section(\n", "    width=2,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "c = gf.components.ring_single_bend_coupler(\n", "    radius=50,\n", "    gap=2,\n", "    coupling_angle_coverage=120,\n", "    bend=\"bend_circular\",\n", "    bend_output=\"bend_euler\",\n", "    length_x=0,\n", "    length_y=0,\n", "    cross_section_inner=custom_in,\n", "    cross_section_outer=custom_out\n", ").copy()\n", "c.draw_ports()\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 3, "id": "01334dae", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "module 'gdsfactory.component' has no attribute 'coupler_bend'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      2\u001b[39m c = gf.Component()\n\u001b[32m      3\u001b[39m custom_in = gf.cross_section.cross_section(\n\u001b[32m      4\u001b[39m     width=\u001b[32m1.5\u001b[39m,\n\u001b[32m      5\u001b[39m     offset=\u001b[32m0\u001b[39m,\n\u001b[32m      6\u001b[39m     layer=(\u001b[32m1\u001b[39m, \u001b[32m0\u001b[39m)\n\u001b[32m      7\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m cp = \u001b[43mgf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcomponent\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcoupler_bend\u001b[49m(\n\u001b[32m      9\u001b[39m         radius=\u001b[32m10\u001b[39m,\n\u001b[32m     10\u001b[39m         coupler_gap=\u001b[32m2\u001b[39m,\n\u001b[32m     11\u001b[39m         coupling_angle_coverage=\u001b[32m180\u001b[39m,\n\u001b[32m     12\u001b[39m         cross_section_inner=custom_in,\n\u001b[32m     13\u001b[39m         cross_section_outer=custom_in,\n\u001b[32m     14\u001b[39m \n\u001b[32m     15\u001b[39m     )\n\u001b[32m     16\u001b[39m cp.show()\n\u001b[32m     17\u001b[39m sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "\u001b[31mAttributeError\u001b[39m: module 'gdsfactory.component' has no attribute 'coupler_bend'"]}], "source": ["import gdsfactory as gf\n", "c = gf.Component()\n", "custom_in = gf.cross_section.cross_section(\n", "    width=1.5,\n", "    offset=0,\n", "    layer=(1, 0)\n", ")\n", "cp = gf.component.coupler_bend(\n", "        radius=10,\n", "        coupler_gap=2,\n", "        coupling_angle_coverage=180,\n", "        cross_section_inner=custom_in,\n", "        cross_section_outer=custom_in,\n", "       \n", "    )\n", "cp.show()\n", "sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "coupler_right = c << cp\n", "coupler_left = c << cp\n", "straight_inner = c << sin\n", "straight_inner.movex(-length_x / 2)\n", "straight_outer = c << sout\n", "straight_outer.movex(-length_x / 2)\n", "\n", "coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "  "]}, {"cell_type": "code", "execution_count": null, "id": "27ba2c38", "metadata": {}, "outputs": [], "source": ["@gf.cell_with_module_name\n", "def coupler_ring_bend(\n", "    radius: float | None = None,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    length_x: float = 0.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", ") -> Component:\n", "    r\"\"\"Two back-to-back coupler_bend.\n", "\n", "    Args:\n", "        radius: um. Default is None, which uses the default radius of the cross_section.\n", "        coupler_gap: um.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        coupling_angle_coverage: degrees.\n", "        length_x: horizontal straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        straight: for straight.\n", "    \"\"\"\n", "    c = Component()\n", "    cp = coupler_bend(\n", "        radius=radius,\n", "        coupler_gap=coupler_gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend,\n", "        bend_output=bend_output,\n", "    )\n", "    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "    sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "    coupler_right = c << cp\n", "    coupler_left = c << cp\n", "    straight_inner = c << sin\n", "    straight_inner.movex(-length_x / 2)\n", "    straight_outer = c << sout\n", "    straight_outer.movex(-length_x / 2)\n", "\n", "    coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "    straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "    coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "    straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "    c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "    c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "    return c"]}, {"cell_type": "code", "execution_count": 9, "id": "f77b67d2", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "\n", "c = gf.components.coupler_bent(gap=0.2, radius=26, length=5, width1=0.4, width2=0.4, length_straight=0, cross_section='strip').copy()\n", "c.draw_ports()\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 15, "id": "9df2d650", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:1443: RuntimeWarning: divide by zero encountered in scalar divide\n", "  npoints = max(int(npoints), int(360 / angle) + 1)\n"]}, {"ename": "OverflowError", "evalue": "cannot convert float infinity to integer", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOverflowError\u001b[39m                             Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 137\u001b[39m\n\u001b[32m    133\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m c\n\u001b[32m    136\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[34m__name__\u001b[39m == \u001b[33m\"\u001b[39m\u001b[33m__main__\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m137\u001b[39m     c = \u001b[43mcoupler_bent_half\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    138\u001b[39m     \u001b[38;5;66;03m# c = coupler_bent_half()\u001b[39;00m\n\u001b[32m    139\u001b[39m     \u001b[38;5;66;03m# c = coupler_bent()\u001b[39;00m\n\u001b[32m    140\u001b[39m     c.plot()\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\layout.py:1003\u001b[39m, in \u001b[36mKCLayout.cell.<locals>.decorator_autocell.<locals>.func\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m   1001\u001b[39m \u001b[38;5;129m@functools\u001b[39m.wraps(f)\n\u001b[32m   1002\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mfunc\u001b[39m(*args: KCellParams.args, **kwargs: KCellParams.kwargs) -> KC:\n\u001b[32m-> \u001b[39m\u001b[32m1003\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapper_autocell\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:458\u001b[39m, in \u001b[36mWrappedKCellFunc.__call__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m    457\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__call__\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args: KCellParams.args, **kwargs: KCellParams.kwargs) -> KC:\n\u001b[32m--> \u001b[39m\u001b[32m458\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_f\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:432\u001b[39m, in \u001b[36mWrappedKCellFunc.__init__.<locals>.wrapper_autocell\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    429\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m output_type(base=cell.base)\n\u001b[32m    431\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m kcl.thread_lock:\n\u001b[32m--> \u001b[39m\u001b[32m432\u001b[39m     cell_ = \u001b[43mwrapped_cell\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    433\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m cell_.destroyed():\n\u001b[32m    434\u001b[39m         \u001b[38;5;66;03m# If any cell has been destroyed, we should clean up the cache.\u001b[39;00m\n\u001b[32m    435\u001b[39m         \u001b[38;5;66;03m# Delete all the KCell entrances in the cache which have\u001b[39;00m\n\u001b[32m    436\u001b[39m         \u001b[38;5;66;03m# `destroyed() == True`\u001b[39;00m\n\u001b[32m    437\u001b[39m         deleted_cell_hashes: \u001b[38;5;28mlist\u001b[39m[\u001b[38;5;28mint\u001b[39m] = [\n\u001b[32m    438\u001b[39m             _hash_item\n\u001b[32m    439\u001b[39m             \u001b[38;5;28;01mfor\u001b[39;00m _hash_item, _cell_item \u001b[38;5;129;01min\u001b[39;00m cache.items()\n\u001b[32m    440\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m _cell_item.destroyed()\n\u001b[32m    441\u001b[39m         ]\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\cachetools\\_cached.py:173\u001b[39m, in \u001b[36m_locked.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    171\u001b[39m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m:\n\u001b[32m    172\u001b[39m         \u001b[38;5;28;01mpass\u001b[39;00m  \u001b[38;5;66;03m# key not found\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m173\u001b[39m v = \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    174\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m lock:\n\u001b[32m    175\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    176\u001b[39m         \u001b[38;5;66;03m# in case of a race, prefer the item already in the cache\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\kfactory\\decorators.py:383\u001b[39m, in \u001b[36mWrappedKCellFunc.__init__.<locals>.wrapper_autocell.<locals>.wrapped_cell\u001b[39m\u001b[34m(**params)\u001b[39m\n\u001b[32m    381\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    382\u001b[39m     name_ = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m383\u001b[39m cell = \u001b[43mf\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# type: ignore[call-arg]\u001b[39;00m\n\u001b[32m    384\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m cell \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    385\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    386\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mThe cell function \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mself\u001b[39m.name\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m.file)\u001b[38;5;132;01m!r}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    387\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m returned None. Did you forget to return the cell or component\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    388\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m at the end of the function?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    389\u001b[39m     )\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 40\u001b[39m, in \u001b[36mcoupler_bent_half\u001b[39m\u001b[34m(gap, radius, length, width1, width2, length_straight, length_straight_exit, cross_section)\u001b[39m\n\u001b[32m     37\u001b[39m xs1 = xs.copy(radius=radius_outer, width=width1)\n\u001b[32m     38\u001b[39m xs2 = xs.copy(radius=radius_inner, width=width2)\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m outer_bend = \u001b[43mgf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m.\u001b[49m\u001b[43marc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mangle\u001b[49m\u001b[43m=\u001b[49m\u001b[43m-\u001b[49m\u001b[43malpha\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mradius\u001b[49m\u001b[43m=\u001b[49m\u001b[43mradius_outer\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     41\u001b[39m inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)\n\u001b[32m     43\u001b[39m outer_straight = gf.path.straight(length=length, npoints=\u001b[32m100\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\python\\python3_12\\Lib\\site-packages\\gdsfactory\\path.py:1443\u001b[39m, in \u001b[36marc\u001b[39m\u001b[34m(radius, angle, npoints, start_angle)\u001b[39m\n\u001b[32m   1440\u001b[39m     \u001b[38;5;28;01<PERSON>raise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33marc() requires a radius argument\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m   1442\u001b[39m npoints = npoints \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mabs\u001b[39m(\u001b[38;5;28mint\u001b[39m(angle / \u001b[32m360\u001b[39m * radius / PDK.bend_points_distance / \u001b[32m2\u001b[39m))\n\u001b[32m-> \u001b[39m\u001b[32m1443\u001b[39m npoints = \u001b[38;5;28mmax\u001b[39m(\u001b[38;5;28mint\u001b[39m(npoints), \u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[32;43m360\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m/\u001b[49m\u001b[43m \u001b[49m\u001b[43mangle\u001b[49m\u001b[43m)\u001b[49m + \u001b[32m1\u001b[39m)\n\u001b[32m   1445\u001b[39m t = np.linspace(\n\u001b[32m   1446\u001b[39m     start_angle * np.pi / \u001b[32m180\u001b[39m, (angle + start_angle) * np.pi / \u001b[32m180\u001b[39m, npoints\n\u001b[32m   1447\u001b[39m )\n\u001b[32m   1448\u001b[39m x = radius * np.cos(t)\n", "\u001b[31mOverflowError\u001b[39m: cannot convert float infinity to integer"]}], "source": ["import numpy as np\n", "\n", "import gdsfactory as gf\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bent_half(\n", "    gap: float = 0.200,\n", "    radius: float = 26,\n", "    length: float = 0,\n", "    width1: float = 0.400,\n", "    width2: float = 0.400,\n", "    length_straight: float = 0,\n", "    length_straight_exit: float = 0,\n", "    cross_section: str = \"strip\",\n", ") -> gf.Component:\n", "    \"\"\"Returns Broadband SOI curved / straight directional coupler.\n", "\n", "    Args:\n", "        gap: gap.\n", "        radius: radius coupling.\n", "        length: coupler_length.\n", "        width1: width1.\n", "        width2: width2.\n", "        length_straight: input and output straight length.\n", "        length_straight_exit: length straight exit.\n", "        cross_section: cross_section.\n", "    \"\"\"\n", "    radius_outer = radius + (width1 + gap) / 2\n", "    radius_inner = radius - (width2 + gap) / 2\n", "    alpha = round(np.rad2deg(length / (2 * radius)), 4)\n", "    beta = alpha\n", "\n", "    c = gf.Component()\n", "\n", "    xs = gf.get_cross_section(cross_section)\n", "    xs1 = xs.copy(radius=radius_outer, width=width1)\n", "    xs2 = xs.copy(radius=radius_inner, width=width2)\n", "\n", "    outer_bend = gf.path.arc(angle=-alpha, radius=radius_outer)\n", "    inner_bend = gf.path.arc(angle=-alpha, radius=radius_inner)\n", "\n", "    outer_straight = gf.path.straight(length=length, npoints=100)\n", "    inner_straight = gf.path.straight(length=length, npoints=100)\n", "\n", "    outer_exit_bend = gf.path.arc(angle=alpha, radius=radius_outer)\n", "    inner_exit_bend_down = gf.path.arc(angle=-beta, radius=radius_inner)\n", "    inner_exit_bend_up = gf.path.arc(angle=alpha + beta, radius=radius_inner)\n", "\n", "    inner_exit_straight = gf.path.straight(\n", "        length=length_straight,\n", "        npoints=100,\n", "    )\n", "    outer_exit_straight = gf.path.straight(\n", "        length=length_straight_exit,\n", "        npoints=100,\n", "    )\n", "\n", "    outer = outer_bend + outer_straight + outer_exit_bend + outer_exit_straight\n", "    inner = (\n", "        inner_bend\n", "        + inner_straight\n", "        + inner_exit_bend_down\n", "        + inner_exit_bend_up\n", "        + inner_exit_straight\n", "    )\n", "\n", "    inner_component = c << inner.extrude(xs2)\n", "    outer_component = c << outer.extrude(xs1)\n", "    outer_component.movey(+(width1 + gap) / 2)\n", "    inner_component.movey(-(width2 + gap) / 2)\n", "\n", "    c.add_port(\"o1\", port=outer_component.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=inner_component.ports[\"o1\"])\n", "    c.add_port(\"o3\", port=outer_component.ports[\"o2\"])\n", "    c.add_port(\"o4\", port=inner_component.ports[\"o2\"])\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bent(\n", "    gap: float = 0.200,\n", "    radius: float = 26,\n", "    length: float = 8.6,\n", "    width1: float = 0.400,\n", "    width2: float = 0.400,\n", "    length_straight: float = 10,\n", "    cross_section: str = \"strip\",\n", ") -> gf.Component:\n", "    \"\"\"Returns Broadband SOI curved / straight directional coupler.\n", "\n", "    based on: https://doi.org/10.1038/s41598-017-07618-6.\n", "\n", "    Args:\n", "        gap: gap.\n", "        radius: radius coupling.\n", "        length: coupler_length.\n", "        width1: width1.\n", "        width2: width2.\n", "        length_straight: input and output straight length.\n", "        cross_section: cross_section.\n", "    \"\"\"\n", "    c = gf.Component()\n", "\n", "    right_half = c << coupler_bent_half(\n", "        gap=gap,\n", "        radius=radius,\n", "        length=length,\n", "        width1=width1,\n", "        width2=width2,\n", "        length_straight=length_straight,\n", "        cross_section=cross_section,\n", "    )\n", "    left_half = c << coupler_bent_half(\n", "        gap=gap,\n", "        radius=radius,\n", "        length=length,\n", "        width1=width1,\n", "        width2=width2,\n", "        length_straight=length_straight,\n", "        cross_section=cross_section,\n", "    )\n", "\n", "    left_half.connect(port=\"o1\", other=right_half.ports[\"o1\"], mirror=True)\n", "\n", "    c.add_port(\"o1\", port=left_half.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=left_half.ports[\"o4\"])\n", "    c.add_port(\"o3\", port=right_half.ports[\"o3\"])\n", "    c.add_port(\"o4\", port=right_half.ports[\"o4\"])\n", "\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    c = coupler_bent_half()\n", "    # c = coupler_bent_half()\n", "    # c = coupler_bent()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 17, "id": "0f8d6b7d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from __future__ import annotations\n", "\n", "import gdsfactory as gf\n", "from gdsfactory.component import Component\n", "from gdsfactory.components.couplers.coupler import coupler_straight\n", "from gdsfactory.components.couplers.coupler90 import coupler90\n", "from gdsfactory.typings import ComponentSpec, CrossSectionSpec\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_ring(\n", "    gap: float = 0.2,\n", "    radius: float = 5.0,\n", "    length_x: float = 4.0,\n", "    bend: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", "    cross_section: CrossSectionSpec = \"strip\",\n", "    cross_section_bend: CrossSectionSpec | None = None,\n", "    length_extension: float = 3.0,\n", ") -> Component:\n", "    r\"\"\"<PERSON><PERSON><PERSON> for ring.\n", "\n", "    Args:\n", "        gap: spacing between parallel coupled straight waveguides.\n", "        radius: of the bends.\n", "        length_x: length of the parallel coupled straight waveguides.\n", "        bend: 90 degrees bend spec.\n", "        straight: straight spec.\n", "        cross_section: cross_section spec.\n", "        cross_section_bend: optional bend cross_section spec.\n", "        length_extension: straight length extension at the end of the coupler bottom ports.\n", "\n", "    .. code::\n", "\n", "          o2            o3\n", "           |             |\n", "            \\           /\n", "             \\         /\n", "           ---=========---\n", "        o1    length_x   o4\n", "\n", "          o2                              o3\n", "          xx                              xx\n", "          xx                             xx\n", "           xx          length_x          x\n", "            xx     ◄───────────────►    x\n", "             xx                       xxx\n", "               xx                   xxx\n", "                xxx──────▲─────────xxx\n", "                         │gap\n", "                 o1──────▼─────────◄──────────────► o4\n", "                                    length_extension\n", "    \"\"\"\n", "    if length_extension is None:\n", "        length_extension = 3 + radius\n", "\n", "    c = Component()\n", "    gap = gf.snap.snap_to_grid(gap, grid_factor=2)\n", "    cross_section_bend = cross_section_bend or cross_section\n", "\n", "    # define subcells\n", "    coupler90_component = gf.get_component(\n", "        coupler90,\n", "        gap=gap,\n", "        radius=radius,\n", "        bend=bend,\n", "        straight=straight,\n", "        cross_section=cross_section,\n", "        cross_section_bend=cross_section_bend,\n", "        length_straight=length_extension,\n", "    )\n", "    coupler_straight_component = gf.get_component(\n", "        coupler_straight,\n", "        gap=gap,\n", "        length=length_x,\n", "        cross_section=cross_section,\n", "    )\n", "\n", "    # add references to subcells\n", "    cbl = c << coupler90_component\n", "    cbr = c << coupler90_component\n", "    cs = c << coupler_straight_component\n", "\n", "    # connect references\n", "    cs.connect(port=\"o4\", other=cbr.ports[\"o1\"])\n", "    cbl.connect(port=\"o2\", other=cs.ports[\"o2\"], mirror=True)\n", "\n", "    c.add_port(\"o1\", port=cbl.ports[\"o4\"])\n", "    c.add_port(\"o2\", port=cbl.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=cbr.ports[\"o3\"])\n", "    c.add_port(\"o4\", port=cbr.ports[\"o4\"])\n", "\n", "    c.add_ports(\n", "        gf.port.select_ports_list(ports=cbl.ports, port_type=\"electrical\"), prefix=\"cbl\"\n", "    )\n", "    c.add_ports(\n", "        gf.port.select_ports_list(ports=cbr.ports, port_type=\"electrical\"), prefix=\"cbr\"\n", "    )\n", "    c.auto_rename_ports()\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    c = coupler_ring()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 22, "id": "0d34b2ac", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                        </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.5   │ 0.0         │ WG (1/0) │ (11.402000000000001, -4.598)  │ optical   │\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (-11.402000000000001, -4.598) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                       \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o2   │ 0.5   │ 0.0         │ WG (1/0) │ (11.402000000000001, -4.598)  │ optical   │\n", "│ o1   │ 0.5   │ 180.0       │ WG (1/0) │ (-11.402000000000001, -4.598) │ optical   │\n", "└──────┴───────┴─────────────┴──────────┴───────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAzAAAAJoCAYAAAC5ogQ1AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAMTgAADE4Bf3eMIwAAJqlJREFUeJzt3Q1yG8mVhdGHid6pNoKNaK2YUFu0IYjEH6sq78s8X8SJmrHlcRIEsvIV2ZrT5XK5lCRJkiQ16P9GL0CSJEmSns0AI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUpn8e/YHT6XTMSiRJkiQt3+XB/5tKP4GRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUpvgB5nw+V0IJ60hYQ8o6EtaQsg5ryFpHwhpS1mENWetIWEPKOqwhax0Ja0hZR8IaktbxVafL5XK5+wdOp+NWI0mSJGnpLvfHk/yfwEiSJEnSRwYYSZIkSW0ywEiSJElqkwFGkiRJUpsMMJIkSZLaZICRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTQYYSZIkSW0ywEiSJElqkwFGkiRJUpsMMJIkSZLaZICRJEmS1CYDjCRJkqQ2xQ8w5/O5EkpYR8IaUtaRsIaUdVhD1joS1pCyDmvIWkfCGlLWYQ1Z60hYQ8o6EtaQtI6vOl0ul8vdP3A6HbcaSZIkSUt3uT+e5P8ERpIkSZI+MsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTQYYSZIkSW0ywEiSJElqkwFGkiRJUpsMMJIkSZLaZICRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTfEDzPl8roQS1pGwhpR1JKwhZR3WkLWOhDWkrMMastaRsIaUdVhD1joS1pCyjoQ1JK3jq06Xy+Vy9w+cTsetRpIkSdLSXe6PJ/k/gZEkSZKkjwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTQYYSZIkSW0ywEiSJElqkwFGkiRJUpsMMJIkSZLaZICRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbfpn9AIkScd1+fmzZu3048foJUiSDshPYCRpouHkkeuD/mzXZ79+SVLzLg/69UcAyHD5+fOujz/j+vn10WsHQA33cD4xwADkMaTkDDej3wsAq3mUAQYggCEl+2qgAajDGGAAAvnJSt+rgQagdmWAAQhgWJn7aqABqM0YYAAG8BOWda8GGoBae4A5n8/D15CyjoQ1pKwjYQ0p67CGnHUkDSu/XouEdYy+prwOCcPM6M9H0jqsIWsdCWtIWUfCGipgHe0HGIBkSUOLa49rwjADUMEMMAAbM7S4bnU1zADUXwwwABswtLjufTXMANS/DDAAbzK0uI66GmaAlT3KAANww9DimnQ1yACreZQBBuDmqXfCodXV9XaIMcwAq3iUAQZYmqHFtdvVIAPU5AwwAJ8wtLz+5H+E0V9/+tUgA9SEDDAAv616OP7uAJHy/TL8PP7+jv6MAdQGDDDA8lY41KYOIQnf85WGm5W/50BNwwADLGvWw6ohZZv3xexDjfcFUE0ZYIDlzHQoNaiMed/M9v4Z/doC1AsMMMAyuh86DSt5ZhpqvKeAasIAA0yv8+HSwNJL92HGew2oBh51+j2kfNnpdLr3b0vSsC4/f/57Pf348e//nH697de/ntBna0sv+bXr9H5MeR0l6boH48njEWf0BAZwq9MT8JFPvT/79aevjH6dvvO6Pvq6Rr7mXV7Ho18ngLrDr5ABU0k49D26HnkwnGkoOeL7MfJ7M/p1eOYKUAEMMMAU0g+BRxyMDSn7DzdHfe+SX4+9XgOAepIBBmgt+dC358HXsJLxfT3ie5v69W/9dQPUkwwwQEvph7utD3jph9rVr6t+3w0yQA1ggAHaWeEwd3twTfj6XF8fZvZ4T8z8vgeoJxhggDYSD/NbHuASvz5X75Ojvz6AesAAA7SQcEhb5TDq6v3z3StA7cgAA0RLO5xtcUBLPXS69h1m0t5PBhmgdmSAAWKlHMq2OJAZWlxXe58ZYoDaiQEGiJNwCJvtMOna6zrL+88gA9QODDBAlJTD43e/hoSvw3WO60zvR4DagAEGiDD6kPXdg+L1/43Rh0TX+a5bv0dHfh3vrh+gfjPAAMN1PlQlHApd17pu9Z7t+HkDKAMMMNrow1Tndbu6fvc93HHdAI8ywAC7+DjEjDo8vXOAuv7PJhxeXV0/e0/O/jkEeJQBBtjc6MPeO+tdbWiZzWrft3ff4yPXD1CrDDDn83n4GlLWkbCGlHUkrCFlHWlrGHmoe/W1SDjUHfG6fHXgn+W9uepw8+r38df3Y/TrcuT7IuG9mb6GlHUkrCFlHQlrqIB1tB9ggB5GHYreOYyPPsQdMaSMfj8kmXm4+e5nYMR6R78fgIpngAF2N/qJbod17jWsjP7edzbbUPPuZ6LDOoG1PMoAA3zL0Ye+bk+ct/p6HfqOfT93fr9852tOXiewjkcZYIC3dTj0dDqMGljyfPV9Sb5+97Ny5DpHf3+BimSAATbX4Yltt8Omw1wPHYeZd76+I9fnvQ/UDQMMsKlRh7FX1pd+uHRwm0OXYebdQWbEFaAMMMCWHLoMLfQeZt79mhLXB9S0DDDAJo4+7L+6ttGHQkMLXYaZDp8vYG2PMsAAD6UerhIPiYYWOgwz737WjlwnsK5HGWCAdoeWtMPg7Zpg1vdt6rqAmooBBnibJ8Hbrx9u3zcJ13c+g0etD1jPowwwwF+OOpx0HV4MLcw4zKQOMT5rsJ5HGWCAPyQeSroe8uBZSe/vxIcKPnewlkcZYIDow0jHQx28K2lYf3XNe6/HZxDW8SgDDBB7CBl9eHNoYhRDjM8jrOxRBhjg0MNS2nq+OiQ5KJHg9v2YPsSnrQeolgwwwF2GF4cj8nX6XKStB6h2DDDAlwwvfuJCL7fvV0PM+O8JUJszwADxw0uXQxmk6DDs+/wC9SYDDBA/vBx9dfBhFiOH/2fXt/fVZxlqOgYY4A97H3bSntB+tr7R3wPYWvoQc8S+M/p7ANRmDDDAf6UcIkYdthxymFn65ypl/wEqngEGiPrJy9GHLAcbVnPETzxur6989kevA6h4Bhgg5tCQeKiCWaV+3lL2I6BiGWBgcSmHhdTDFMws9XOXsi8BFckAAwtLOSQcfYga/bpDGkMMUI0YYGBRqw0vDixw3+3nZIX9wZ4A1ZIBBhaUcihIOywBeZ/LlP0KqBgGGFhMymEg7ZAE5H4+U/YtoCIYYGAhKYeAtMMRkP85Tdm/gBrOAAOLSLj5f/y5Iw5Fo19vmMVRQ8wre8ie6xj9egP1kAEGFpBw0z9qaHEAge0lfX4T9jOghjLAwOQSbvZJT3CB99x+zgwxQA3SfoA5n8/D15CyjoQ1pKwjYQ0J60i4yf/6M79eh9HrSPh+pKwhZR3WkLWOZ9ew975yvV+sPMQkvCdS1pGwhpR1JKyhAtbRfoABcm/uR/3kZfRrDavxkxigBjLAwIQSbuqGF5ibIQaoQQwwMJmUm3nKOoD9pHzOU9YB1CEMMDCJj5tswk08ZR3A/lI+70esw94DFcEAA5NY5RAx+nUGPrfC/mMPgopggIEJpNy0U9YBHC/l85+yDqB2Y4CB5lJu1n7yAvyywn5kT4IaygADjaXcpA0vwLUV9iV7E9QwBhhoKuXmbHgBPrPC/mSPghrCAAPNfNw0E27KhhfgnhX2KYMM1OEMMNCI4QXoZoX9yhADdSgDDDSx97CQdBgY/VoD21pl37J/QR3CAAMNJA0vR6wHmE/CvmGIgZqCAQbCGV6AWSTsH4YYqPYMMBDM8ALMJmEfMcRAtWaAgVCGF2BWCfuJIQaqLQMMBEobXm7/M27uwHfNvq/Z56B2Y4CBMIk3U8MLMOsQs9c6PrsCtQkDDIRIfRJoeAH2tNIQY++D2oQBBhYaXl69ee61LjdxIG2fSd2HgfqLAQYGW+0nL27eQPJ+Y4iBimeAgYFSh5e91uGmDYzYD99Zy95X+yHU2wwwMIjhBeBPhhignmCAgQFWHF7cqIFHbveLGffD26u9EeplBhiY6Ob83Zv19Rq3WoebM9B5HzpqiLFXQj3NAAMHOWpoSRpevrseYE1pP+nosH/DSh5lgIFvOvInLoYXYBYrDzH2Tqi7DDCwsw7DS+J6ANL2pS77OdTkDDDQ/CcvWzytc/MFUqXtT7f7rp/EQB3OAAM76HRzS10XwN77aeq67KdQdxlgYGPdbmpbr9fNFtha6j7Vbb+HmoQBBjbU7WaWeigA6LJfddv3oSbQfoA5n8/D15CyjoQ1pKzj6DV83FwSb2L3Xouj1pXwnkhZR8IaUtZhDVnrSFjDo3Xssc9u8Vocuf8fPcgkvC8S1pCyjoQ1VMA62g8wMNoRN62P65Y3rr3WCbCX5H3rqCFm63VDNWSAgSY3K8MLQPb+1fm+ANWIAQYWfNK2x/oBjpI+BBhioHZlgIEXGV7cPIGxOuxjhhio3Rhg4EkfN4nOw8vW63PTBEbp8JPko4cYezKreJQBBg6+CV1fk78ON0pgNEPMcV8HVBADDNwx4icue958OtzsAWZ7KDPiPmKPpiZmgIGQ4eWIG6jhBZhNl0P/7T5viIF6mwEGFrjJbP31uCkCKbrtb7M9HIMawAADg24qt/+de39tndYLMPNPmGe+30AdwAADC9xMut3cAV5hX877+qB2ZIBhabc/Xj/6euTX2G3dAK/oesgfNcTYz6nGDDAsa9TQYngB2Ichxr7OGgwwLGfkj+2Pvll0vZkDrPbQ5vY+cfT9afT3DuoFBhiWsdrNofv6AVYbYlZ7yAb1JgMM0xt1M7j9757h6wZIZ//u+3VDPckAw9RWvQkYXoCV2cf7ft1QTzDAMKXbH4ePuo78+re6jv5aAFZ+iDN6iBn99UN9wgDDVEYPLSmb/VavQ8LXArD6PpjwUC7hdYD6zQDDFGzuf78WW359AN3Y3+d9SAePMsAQK2FouV5H0uuy5RWgqxn3w6T7XtLrwloeZYAhUtLwkva6bHVN+9oAXrXHfSLF6PueIYYayABDG0lDS+qmvdXrk/i1Abxj5n3RfZFVPcoAw3DXG2PKJp38Om359QJ0Z/8/fohJfZ2oaRhgiJU0tFyvJ/n12vIKMIsV9sfE+2Xy60W1ZoAhTtomfH1d4XXr8LUCvGKl/XH0fbLr60a1YoAhwmc/ek64dtl4V7o5A7xjpX0y+X7a4fWj4hlgGCpxk71eV6fXccsrwGxW3CeT76+dXkcqjgGGw6UPLd021RVvygDvWHW/dN+lJmOA4TCpm+ft2lZ9Xbt+/QDPWn2/dB+mJmGAYVfXT1cSNscZN81VnygCvMp+2WOI6X5fpnZngGHpoaX7BulmDPAa++b/Xocu9+nurzW1OQMMSw0t12ud6XX3egA8x765z+ux99UwQ800wJzP5+FrSFnH0Wv4amj5tY6Eze72OmLj2/t78szX/cr3o+vr0GkdCWtIWYc1ZK0jYQ1HrMO++fXrkjrM3H4/Zr2nd1lDBayj/QDDgW+Wm00jYVN79jrjE5utXyeAVdg/7782CfftZ68jBxpqGAMM998gDYeVz9Y9o62+LzO/RgCfsX8+/xol3M/f+b7M/L2hDDDcvCGa/oTl9rrCxuV1Anjflve5mc1wHnC/q+kYYBZ3++FO2Gzeva60QW39+gGsxj76+us1yzlhpfNCTcoAs5CZhpXb62ob0Vbfv9VeN4AP9tGxr1vK1VBTLRlgJjXzsHL79a36/d3i9Rv9dQCMtOX9cTUrnC/cKyuWAaa5zz5oCR/+va42lO1fV4BV2U+3eQ1XO3+sfg6pAAaYcF99aFbYLG6vNos/3xNeT4DvsZ9mvp5drs+cz6h93msGmIEvvuHk4dUm8Pn7ZovXdfTXAZBgy/stf76mCeeI0VdDTu3zHjPAbPAiPvEGNZwYWrZ+v23xegOszkOh/V9f557vDTkGn/r7ffWg0+8h5ctOp1Nt1eXnz+ra6cePf9fv+t719rXU1231enudJel/Z48t7mf6us/u9Qnnj67Xrp02+pw8GE8ejzh7TOmjJ2HX4584jJ7ku/CkEGB7W54/eP71du5b63rZ8DMS9StkCS+u63Fv4i3fyKvYcrMHoCIPZ6u4PQ+4rnGtGQeY0S+q635XG3zG58P3AOBP9tfxbs8JrvNdLzMPMK5zXW3ouZ8PAP7D/prl9vzgOte1ZhxgRr+ort+/Xn8fyfx8APCnre5/o7+O2dyeK1z7Xi8zDzCuva63tnw/sP3nw/cI4HNbHpLZ93t0e+5w7XWtGQeY0S+q62tDy5bff477fADwp63uj6O/jpV8diZxzb1OPcC4Zl5vNwjGbNJbfD8B+FzaIY3Xvne3ZxXXn5HXKQeY0S+qq18LS+SmCrC/Lc8hZHwvb88zrj+HXaceYFzHDis23kxuqgD7Szukse331lDzM+I65QAz+kWd+WpY6ctNFWB/W55DyGeoqcPPodMOMK6GFfb9XADwOfsthpra/TrlADP6Re10NaisYcvPBQD3bXV/Hv11sP37wnBTMZ+LuAHG9bkhxea4jrRNA2BmHhrximfOaq4//7hOOcCsPpQYULjlZgpwHA+N2NKr576Zr5eZB5jU61ZGf5DoJ23TAJiZh0aMsvV58xJ63eS1etDp95DyZafTqbbq8vNnpXb68WP0ErRoH5+LX+/BX//zd66SpPt9d5+93rOlEV0WOE8/GE8ejzijp1WYXdITD4AV2HehokX9ChnwJ78+BnA8v0YGFc0AA8HcRAGO5+ERVDQDDARzEwU4nodHUNHaDzDn83n4GlLWkbCGlHUkrGGLdcxyE53l+zHLGlLWYQ1Z60hYQ8o6Eh4eJbwOKetIWEPKOhLWUAHraD/AwMxmGF4AOrL/QsUywECw7/4Exg0U4D32X6hYBhgI5gkgwBj2X6hYBhgI5gkgwBj2X6hYBhgI5gkgwBj2X6hYBhgIlfC34ACsapa/BRJqQgYYCOXmCTCOh0hQsQwwEMrNE2AcD5GgYhlgIJSbJ8A4HiJBxTLAQCg3T4BxPESCimWAgVBungDjeIgEFcsAA6HcPAHG8RAJKpYBBkK5eQKM4yESVCwDDIRy8wQYx0MkqFgGGAjl5gkwjodIULEMMBDK8AIwln0YKpIBBib9CYwbJ8D32IehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIhlgIJQnfwBj2YehIrUfYM7n8/A1pKwjYQ0p60hYw3fXsdWTv4TXImENKetIWEPKOqwhax0Ja0hZx8caRv4EJuF1SFlHwhpS1pGwhgpYR/sBBmblyR/AWPZhqEgGGAjld68BxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywEAoT/4AxrIPQ0UywMDET/7cPAHGDC/XV6A2ZYCBUG6eAON4iAQVywADodw8AcbxEAkqlgEGQrl5AozjIRJULAMMhHLzBBjHQySoWAYYCOXmCTCOh0hQsQwwEMrNE2AcD5GgYhlgIJSbJ8A4HiJBxTLAQCg3T4BxPESCimWAgVBungDjeIgEFcsAA6HcPAHG8RAJKpYBBkK5eQKM4yESVCwDDEw8xLh5ArzH/gsVywADwfwEBmAM+y9ULAMMBPMEEGAM+y9ULAMMBPMEEGAM+y9ULAMMBPMEEGAM+y9ULAMMBPO34AAcz98CCRXNAAPB3EQBjufhEVQ0AwwEcxMFOJ6HR1DRDDAQzE0U4HgeHkFFM8BAMDdRgON5eAQVzQAD4dxEAY5l34WK1n6AOZ/Pw9eQso6ENaSsI2ENW61jhr/Kc6bvxwxrSFmHNWStI2ENKetI2HcTXoeUdSSsIWUdCWuogHW0H2Bgdp4EAhzLvgsVzQAD4fwuNsBx/LOHUPEMMBDOzRTgOB4aQcUzwEA4N1OA43hoBBXPAAPh3EwBjuOhEVQ8Aww0YIgB2J/hBaoFAww04KYKsD8Pi6BaMMBAA26qAPvzsAiqBQMMNOCmCrA/D4ugWjDAQANuqgD787AIqgUDDDTgpgqwPw+LoFowwEADWw0vbqwAn7PPQrVhgIGFfgLjxgrwOfssVBsGGGjCr5EB7MfwAtWGAQaacHMF2I+HRFBtGGCgEUMMwPYML1CtGGCgETdZgO15OATVigEGGnGTBdieh0NQrRhgoBE3WYDteTgE1YoBBhrZanhxowX4D/sqVDsGGGjGk0KA7fjJNlQ7Bhhoxs0WYDseCkG1Y4CBZvy6A8A27KdQLRlgoCFPDAG+z0+0oVoywEBDbroA3+dhEFRLBhhoyE0X4Ps8DIJqyQADC9903XiBVdlHodoywEBTnhwCvM/wAtWWAQaa8rfnALzH/gnVmgEGmvLrDwDvsX9CtWaAgaY8QQR4j/0TqjUDDDTmJgzwGvsmVHvtB5jz+Tx8DSnrSFhDyjoS1nDEOrr8GsQq348ua0hZhzVkrSNhDUesw77Zbx0Ja0hZR8IaKmAd7QcYWJ2/jQzgeR2GF6DuMsBAc34dAuA59kuoKRhgoLkuvw4BMJr9EmoKBhiYgJsywH32SahpGGBgAn4tAuA++yTUNAwwMAFPFgHus09CTcMAA5Nwcwb4nP0RaioGGJiEX48A+Jz9EWoqBhiYiJs0wJ/si1DTMcDARPyaBMCf7ItQ0zHAwES2fNLoZg10t9Xwcn0FajgDDEzGzRrgP+yHUFMywMBkPHEE8BNpqIkZYGBCfucbWJ19EGpaBhiYkCePwMr8JBpqagYYmJSbN7Aq+x/U1AwwMClPIIEV+Qk01PQMMDAxvwMOrMa+BzU9AwxMzJNIYCV+8gxrMMDAxNzMgZV4aANrMMDA5AwxwAoML7CORxlgoDk3dWAFHtbAOh5lgIEJGGKAmRleYC2PMsDABNzcgZl5SANreZQBBiZhiAFmZHiB9TzKAAOT2Hp4caPHa8Bo9jVY06MMMDARTyqBmfjJMqzpUQYYmIwhBpiB4QXW9SgDDEzGTR+YgYcxsK5HGWBgQoYYoDPDC6ztUQYYmJB/8BXoyv4FPMoAA5PyBBPoyE+QgUcZYGBSWx8CHASAvdm3gJphgDmfz8PXkLKOhDWkrCNhDSnruLeGow4DCa9DyjoS1pCyDmvIWkfCGu6t48jhJeG1SFhDyjoS1pCyjoQ1VMA62g8wwPd4ogmks08BdcUAA4vzD8QC6exTQF0xwAAOB0As+xNQNwwwwC5/q49DAvBde+xL9iao9gwwwL/8jjmQxr4E1CcMMMB/OSwAKexHQH3BAAP8l1/XABL4tVag7jDAAH8wxAAjGV6AesAAA/zFEAOMYHgB6gkGGOAvDhHACB6eAPUEAwzwKUMMcCTDC1BPMsAAXzLEAEcwvAD1AgMM8KW9hhcHC+CDfQaoFxlggLscLoC92F+AeoMBBnjIIQPYmn0FqDcZYICn+B11YEv+GTug3mSAAZ5miAG2YHgB6hsMMMDwQ4eDB6zDPgLUNxlggJc4fADvsn8AtQEDDPAyhxDgVfYNoDZigAHe4jACPMt+AdSGDDDA2xxKgEfsE0BtzAADvG2PQ8nH1eEE+ttreLm+Aut5lAEGuMsQA3zG8ALUTgwwwLcZYoBrhhegdmSAATZhiAF+MbwAtTMDDNBmiHF4gVy3n1PDC1A7McAAmzLEwHoML0AdyAADbG7vQ4zDDOTweQfqYAYYYBcONTA/n3OgBjDAALtxuIF5+XwDNYgBBtiVQw7Mx+caqIHaDzDn83n4GlLWkbCGlHUkrCFlHaPXcMQ/2Hv935H8WqSsIWUd1pC1jmfWkPR5Hv1arLCGlHUkrCFlHQlrqIB1tB9ggHyrHHpgZj7HQIUwwACHcPiBvnx+gQpigAEO5RAEvRw1vIz+OoFqwwADTDvEOBTB+24/R4YXoEIYYIAhjjoUORzB63w+gQpmgAGWOCQ5KMFjR/7ExWcSqDcZYIChDDGQwfACVBMGGGA4QwyMZXgBqhEDDLDkEOMQBX9/HgwvQDVggAFiHH2IMsiwqhFDi88aUBsxwACX1Z8IO1ixkhGfL58xoDZkgAEiOWTBtjwcAGoSBhggll9zgW349UygJmKAAaKNGmIcvpiBzw9QEzLAAPFGHsIcxOhoxK+LXf/3jv76gZqaAQZoY9SvkzmU0cXIX7v0GQHqIAYYoJXRQ4xDGolG/sTl+gpQBzDAAO0kHNYc2EiQ8DnwWQDqYAYYoC2HN1ZliAdW9igDDBBt9CHudg2wJ+93gDLAAP0lHeoc7Jj1Jy7X6xj9egBre5QBBmgj4XD3cXXIYwspQ8v1FaAGM8AAUxl9uPvsJzIOfrzis/dOyhWgAhhggOkkHv4MMjzifQtQTzHAANNKOwxer8mhkNSh5Xpdo18fgPqEAQaYXvoh0UFxLd6PAPUtBhhgGamHxuu1OTzOKXlouV7f6NcJoJ5ggAGWkn6IvF6jA2VvHYaW63WOfr0A6kkGGGBJ3Q6XDpg9eF8B1O4MMMDSuhw2r9fq4Jnjs+9Jh6v3D1CNGWAAQg6Vr14/M/p1nNm917zbFaAaM8AA/Nb9UGqo2ee9MNP7YvTrClAbMMAA3Oh+WL29Gmwef69nGlY++/6Pfp0BakPtB5jz+Tx8DSnrSFhDyjoS1pCyDmt4fx17HGZ/rSHhUPuxjpEDzhHvi0dfY9r3Y6v/e+98D7t+Tq1h/nUkrCFlHQlrqIB1POr0e0j5stPpdO/flqT2XX7+/ON/P/348e+/Nvt1llb8fv361yRp1h6MJ49HnNETIMCRZvv1Itc5rkf8xAygQrT/FTKAEW5/TcfV9eirwQVY1aMMMAB3GGJcRw0vo9/7ADWIAQZgA7dPxF1dt74aXADqXwYYgI0ZZly3uhpaAOovBhiAnVwfQBMOw649rgYXgLrLAANwAMOMq6EFoDZhgAE4mGHG1dACUG8zwAAMZJhZ62poAahvM8AAhDDMzHk1tADUpgwwAKFuD78Jh3HX54cVAwtA7cIAA9CEYSbzamABqEMZYACa8hMaP2EBWNGjDDAATXx2wE449He9GlgAKpIBBmByXx3GE4aE0VeDCkC186jT7yHly06n071/W5IU3OXnz6f/7OnHj3//fPr1la9HktSvB+NJGWAkSS8NBqMzmEjS3BlgJEmSJE0zwPzfYSuRJEmSpG9mgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTfEDzPl8roQS1pGwhpR1JKwhZR3WkLWOhDWkrMMastaRsIaUdVhD1joS1pCyjoQ1JK3jq06Xy+Vy9w+cTsetRpIkSdLSXe6PJ/k/gZEkSZKkjwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTQYYSZIkSW0ywEiSJElqkwFGkiRJUpsMMJIkSZLaZICRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltih9gzudzJZSwjoQ1pKwjYQ0p67CGrHUkrCFlHdaQtY6ENaSswxqy1pGwhpR1JKwhaR1fdbpcLpe7f+B0Om41kiRJkpbucn88yf8JjCRJkiR9ZICRJEmS1CYDjCRJkqQ2GWAkSZIktckAI0mSJKlNBhhJkiRJbTLASJIkSWqTAUaSJElSmwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKb/hm9gITO5/Pd/12SJElSRssPML+Glc8GGEOMJEmSlNfyv0L23UHls//89b/2MQx9/GuGI0mSJOn9lv8JzBF9NrwYZCRJkqTXW/4nMLcZLCRJkqTc/ATmd9c/GZEkSZKUmQHGT10kSZKkNi3/K2RbDy8GIUmSJGm//ATmib9J7JX/vJ/mSJIkSft1ulwul7t/4HTa8b++fwYWSZIkabsejCd+hUySJElSnwwwkiRJktrkV8gkSZIkxeRXyCRJkiRNkwFGkiRJUpsO/2uU/Y1df+b1kCRJkp5v2f8/MK8ODnv8dckJa3inhHUkrCFlHdaQtY6ENaSswxqy1pGwhpR1WEPWOhLWkLKOhDUkrSPmH+JPeTES1pH+5pAkSZKOzj/EL0mSJGmaDDCSJEmS2mSAkSRJktQm/xC/JEmSpDYd/g/xS5IkSdJX+Yf4JUmSJE2TAUaSJElSmwwwkiRJktpkgJEkSZLUJgOMJEmSpDYZYCRJkiS1yQAjSZIkqU0GGEmSJEltMsBIkiRJapMBRpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTf88+gOXy+WYlUiSJEnSg/wERpIkSVKbDDCSJEmS2mSAkSRJktQmA4wkSZKkNhlgJEmSJLXJACNJkiSpTQYYSZIkSdWl/wcsrR3I7120KwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from __future__ import annotations\n", "\n", "from typing import Any\n", "\n", "import gdsfactory as gf\n", "from gdsfactory.component import Component\n", "from gdsfactory.components.bends.bend_circular import bend_circular_all_angle\n", "from gdsfactory.typings import AnyComponentFactory, ComponentSpec, CrossSectionSpec\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_bend(\n", "    radius: float = 10.0,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 120.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", ") -> Component:\n", "    r\"\"\"Compact curved coupler with bezier escape.\n", "\n", "    TODO: fix for euler bends.\n", "\n", "    Args:\n", "        radius: um.\n", "        coupler_gap: um.\n", "        coupling_angle_coverage: degrees.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "\n", "    .. code::\n", "\n", "            r   4\n", "            |   |\n", "            |  / ___3\n", "            | / /\n", "        2____/ /\n", "        1_____/\n", "    \"\"\"\n", "    c = Component()\n", "\n", "    xi = gf.get_cross_section(cross_section_inner)\n", "    xo = gf.get_cross_section(cross_section_outer)\n", "\n", "    angle_inner = 90\n", "    angle_outer = coupling_angle_coverage / 2\n", "    gap = coupler_gap\n", "\n", "    width = xo.width / 2 + xi.width / 2\n", "    spacing = gap + width\n", "\n", "    bend90_inner_right = gf.get_component(\n", "        bend,  # type: ignore[arg-type]\n", "        radius=radius,\n", "        cross_section=cross_section_inner,\n", "        angle=angle_inner,\n", "    )\n", "    bend_output_right = gf.get_component(\n", "        bend,  # type: ignore[arg-type]\n", "        radius=radius + spacing,\n", "        cross_section=cross_section_outer,\n", "        angle=angle_outer,\n", "    )\n", "    bend_inner_ref = c.create_vinst(bend90_inner_right)\n", "    bend_output_ref = c.create_vinst(bend_output_right)\n", "\n", "    output = gf.get_component(\n", "        bend_output, angle=angle_outer, cross_section=cross_section_outer\n", "    )\n", "    output_ref = c.create_vinst(output)\n", "    output_ref.connect(\"o1\", bend_output_ref.ports[\"o2\"], mirror=True)\n", "\n", "    pbw = bend_inner_ref.ports[\"o1\"]\n", "    bend_inner_ref.movey(pbw.center[1] + spacing)\n", "\n", "    c.add_port(\"o1\", port=bend_output_ref.ports[\"o1\"])\n", "    c.add_port(\"o2\", port=bend_inner_ref.ports[\"o1\"])\n", "    c.add_port(\"o3\", port=output_ref.ports[\"o2\"])\n", "    c.add_port(\"o4\", port=bend_inner_ref.ports[\"o2\"])\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def coupler_ring_bend(\n", "    radius: float = 10.0,\n", "    coupler_gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    length_x: float = 0.0,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    bend: AnyComponentFactory = bend_circular_all_angle,\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    straight: ComponentSpec = \"straight\",\n", ") -> Component:\n", "    r\"\"\"Two back-to-back coupler_bend.\n", "\n", "    Args:\n", "        radius: um.\n", "        coupler_gap: um.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        coupling_angle_coverage: degrees.\n", "        length_x: horizontal straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        straight: for straight.\n", "    \"\"\"\n", "    c = Component()\n", "    cp = coupler_bend(\n", "        radius=radius,\n", "        coupler_gap=coupler_gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend,\n", "        bend_output=bend_output,\n", "    )\n", "    sin = gf.get_component(straight, length=length_x, cross_section=cross_section_inner)\n", "    sout = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section_outer\n", "    )\n", "\n", "    coupler_right = c << cp\n", "    coupler_left = c << cp\n", "    straight_inner = c << sin\n", "    straight_inner.movex(-length_x / 2)\n", "    straight_outer = c << sout\n", "    straight_outer.movex(-length_x / 2)\n", "\n", "    coupler_left.connect(\"o1\", straight_outer.ports[\"o1\"])\n", "    straight_inner.connect(\"o1\", coupler_left.ports[\"o2\"])\n", "    coupler_right.connect(\"o2\", straight_inner.ports[\"o2\"], mirror=True)\n", "    straight_outer.connect(\"o2\", coupler_right.ports[\"o1\"])\n", "\n", "    c.add_port(\"o1\", port=coupler_left.ports[\"o3\"])\n", "    c.add_port(\"o2\", port=coupler_left.ports[\"o4\"])\n", "    c.add_port(\"o4\", port=coupler_right.ports[\"o3\"])\n", "    c.add_port(\"o3\", port=coupler_right.ports[\"o4\"])\n", "    # c.flatten()\n", "    return c\n", "\n", "\n", "@gf.cell_with_module_name\n", "def ring_single_bend_coupler(\n", "    radius: float = 5.0,\n", "    gap: float = 0.2,\n", "    coupling_angle_coverage: float = 90.0,\n", "    bend_all_angle: AnyComponentFactory = bend_circular_all_angle,\n", "    bend: ComponentSpec = \"bend_circular\",\n", "    bend_output: ComponentSpec = \"bend_euler\",\n", "    length_x: float = 0.6,\n", "    length_y: float = 0.6,\n", "    cross_section_inner: CrossSectionSpec = \"strip\",\n", "    cross_section_outer: CrossSectionSpec = \"strip\",\n", "    **kwargs: Any,\n", ") -> Component:\n", "    r\"\"\"Returns ring with curved coupler.\n", "\n", "    TODO: enable euler bends.\n", "\n", "    Args:\n", "        radius: um.\n", "        gap: um.\n", "        coupling_angle_coverage: degrees.\n", "        angle_inner: of the inner bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        angle_outer: of the outer bend, from beginning to end. Depending on the bend chosen, gap may not be preserved.\n", "        bend_all_angle: for bend.\n", "        bend: for bend.\n", "        bend_output: for bend.\n", "        length_x: horizontal straight length.\n", "        length_y: vertical straight length.\n", "        cross_section_inner: spec inner bend.\n", "        cross_section_outer: spec outer bend.\n", "        kwargs: cross_section settings.\n", "    \"\"\"\n", "    c = Component()\n", "\n", "    coupler = coupler_ring_bend(\n", "        radius=radius,\n", "        coupler_gap=gap,\n", "        coupling_angle_coverage=coupling_angle_coverage,\n", "        length_x=length_x,\n", "        cross_section_inner=cross_section_inner,\n", "        cross_section_outer=cross_section_outer,\n", "        bend=bend_all_angle,\n", "        bend_output=bend_output,\n", "    )\n", "    cb = c << coupler\n", "\n", "    cross_section = cross_section_inner\n", "    straight = gf.c.straight\n", "    sx = gf.get_component(\n", "        straight, length=length_x, cross_section=cross_section, **kwargs\n", "    )\n", "    sy = gf.get_component(\n", "        straight, length=length_y, cross_section=cross_section, **kwargs\n", "    )\n", "    b = gf.get_component(bend, cross_section=cross_section, radius=radius, **kwargs)\n", "    sl = c << sy\n", "    sr = c << sy\n", "    bl = c << b\n", "    br = c << b\n", "    st = c << sx\n", "\n", "    sl.connect(port=\"o1\", other=cb[\"o2\"])\n", "    bl.connect(port=\"o2\", other=sl[\"o2\"], mirror=True)\n", "    st.connect(port=\"o2\", other=bl[\"o1\"])\n", "    sr.connect(port=\"o1\", other=br[\"o1\"])\n", "    sr.connect(port=\"o2\", other=cb[\"o3\"])\n", "    br.connect(port=\"o2\", other=st[\"o1\"], mirror=True)\n", "\n", "    c.add_port(\"o2\", port=cb[\"o4\"])\n", "    c.add_port(\"o1\", port=cb[\"o1\"])\n", "    c.flatten()\n", "    return c\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # c = coupler_bend()\n", "    # n = c.get_netlist()\n", "    c = ring_single_bend_coupler()\n", "    # c = ring_single_bend_coupler()\n", "    c.pprint_ports()\n", "    c.plot()\n"]}, {"cell_type": "code", "execution_count": 43, "id": "1efe5363", "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> name </span>┃<span style=\"font-weight: bold\"> width </span>┃<span style=\"font-weight: bold\"> orientation       </span>┃<span style=\"font-weight: bold\"> layer    </span>┃<span style=\"font-weight: bold\"> center                                   </span>┃<span style=\"font-weight: bold\"> port_type </span>┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0             │ WG (1/0) │ (0.0, 0.0)                               │ optical   │\n", "│ o2   │ 0.5   │ 45.12489927031235 │ WG (1/0) │ (6.8129681666423165, 4.8643957687988015) │ optical   │\n", "└──────┴───────┴───────────────────┴──────────┴──────────────────────────────────────────┴───────────┘\n", "</pre>\n"], "text/plain": ["┏━━━━━━┳━━━━━━━┳━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mname\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mwidth\u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1morientation      \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mlayer   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mcenter                                  \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mport_type\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━╇━━━━━━━╇━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━┩\n", "│ o1   │ 0.5   │ 180.0             │ WG (1/0) │ (0.0, 0.0)                               │ optical   │\n", "│ o2   │ 0.5   │ 45.12489927031235 │ WG (1/0) │ (6.8129681666423165, 4.8643957687988015) │ optical   │\n", "└──────┴───────┴───────────────────┴──────────┴──────────────────────────────────────────┴───────────┘\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import gdsfactory as gf\n", "import matplotlib.pyplot as plt\n", "\n", "def gvc_bend(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.2,\n", "    peak_curvature: float = 0.3,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"General Variable Curvature Bend.\n", "\n", "    Args:\n", "        final_angle: Final angle in degrees.\n", "        final_curvature: Final curvature in 1/μm.\n", "        peak_curvature: Peak curvature in 1/μm.\n", "        cross_section: Cross-section spec.\n", "        npoints: Number of points for discretization.\n", "\n", "    \"\"\"\n", "    if peak_curvature <= 0 or final_curvature >= 0:\n", "        raise ValueError(\"For this example, assume positive peak_curvature and negative final_curvature.\")\n", "    if peak_curvature + final_curvature <= 0:\n", "        raise ValueError(\"peak_curvature must be > -final_curvature.\")\n", "\n", "    theta = np.deg2rad(final_angle)\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "\n", "    r = -k_final / k_peak + 1\n", "    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)\n", "    sp2 = r * sp1\n", "\n", "    # Discretize\n", "    ds1 = sp1 / (npoints // 2 - 1) if sp1 > 0 else 0\n", "    s1 = np.linspace(0, sp1, npoints // 2)\n", "    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.array([])\n", "    theta_cum1 = np.cumsum(k1) * ds1\n", "\n", "    ds2 = sp2 / (npoints // 2 - 1) if sp2 > 0 else 0\n", "    s2 = np.linspace(0, sp2, npoints // 2)\n", "    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.array([])\n", "    theta_cum2 = np.cumsum(k2) * ds2 + (theta_cum1[-1] if len(theta_cum1) > 0 else 0)\n", "\n", "    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp2 > 0 else theta_cum1\n", "\n", "    ds = np.concatenate((np.full(len(theta_cum1) - 1, ds1), np.full(len(theta_cum2), ds2))) if sp2 > 0 else np.full(len(theta_cum), ds1)\n", "\n", "    # Positions using trapezoid rule approximation, but for simplicity use cumsum * ds assuming dense points\n", "    x = np.cumsum(np.cos(theta_cum) * ds)\n", "    y = np.cumsum(np.sin(theta_cum) * ds)\n", "    points = np.column_stack((x, y))\n", "\n", "    # Create the path using gdsfactory Path\n", "    P = gf.Path()\n", "    <PERSON>.append(points)\n", "    \n", "    # Create the cross-section\n", "    if isinstance(cross_section, str):\n", "        # Use default cross-section if string is provided\n", "        cs = gf.get_cross_section(cross_section)\n", "    else:\n", "        cs = cross_section\n", "    \n", "    # Extrude the path to create the component\n", "    c = gf.path.extrude(P, cs)\n", "\n", "    # Add ports at the beginning and end\n", "    # if len(points) > 0:\n", "    #     # Start port\n", "    #     c.add_port(name=\"o1\", center=(0, 0), orientation=0, cross_section=cs)\n", "        \n", "    #     # End port\n", "    #     final_center = points[-1]\n", "    #     final_orientation = np.rad2deg(theta_cum[-1])\n", "    #     c.add_port(name=\"o2\", center=final_center, orientation=final_orientation, cross_section=cs)\n", "\n", "    return c\n", "\n", "# Example usage\n", "c = gvc_bend()\n", "c.draw_ports()\n", "c.pprint_ports()\n", "c.plot()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 11, "id": "423d9ba9", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import gdsfactory as gf\n", "import numpy as np\n", "@gf.cell\n", "def gvc_bend_optimal(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.1,\n", "    peak_curvature: float = 0.2,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"\n", "    创建一个理论最优的通用可变曲率弯曲 (C2连续)。\n", "\n", "    该弯曲的曲率 k(s) 及其一阶导数 dk/ds 全程连续，\n", "    从而最大限度地减少过渡损耗。\n", "\n", "    Args:\n", "        final_angle: 最终的目标角度 (degrees)。\n", "        final_curvature: 最终的目标曲率 (1/μm)。正=逆时针, 负=顺时针。\n", "        peak_curvature: 路径中的峰值曲率 (1/μm)。这是一个正值，\n", "                        用作损耗/尺寸的控制参数。值越小，弯曲越平缓，\n", "                        损耗越低，但尺寸越大。\n", "        cross_section: 波导的横截面。\n", "        npoints: 离散化点数。\n", "    \"\"\"\n", "    # --- 输入参数验证 ---\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "    \n", "    if k_peak <= 0:\n", "        raise ValueError(\"peak_curvature 必须为正数。\")\n", "    if np.abs(k_final) >= k_peak:\n", "        raise ValueError(\n", "            f\"peak_curvature ({k_peak}) 必须大于等于最终曲率的绝对值 \"\n", "            f\"abs(final_curvature) ({np.abs(k_final)}).\"\n", "        )\n", "\n", "    # --- 核心逻辑: 根据输入参数反解路径长度 L1 和 L2 ---\n", "    # 我们建立一个模型，其中曲率由两段升余弦函数构成，\n", "    # 并假设两段的最大变化率 dk/ds 大小相等，以获得对称平滑的过渡。\n", "    # 最终可以推导出 L1 和 L2 的解析解：\n", "    \n", "    theta_final_rad = np.deg2rad(final_angle)\n", "    \n", "    # 求解 L1 (从 k=0 到 k=k_peak 的路径长度)\n", "    denominator = (k_peak / 2) + (k_peak + k_final) / 2 * (k_peak - k_final) / k_peak\n", "    if np.isclose(denominator, 0):\n", "        # 避免除以零\n", "        denominator = 1e-9\n", "    L1 = theta_final_rad / denominator\n", "\n", "    # 求解 L2 (从 k=k_peak 到 k=k_final 的路径长度)\n", "    L2 = L1 * (k_peak - k_final) / k_peak\n", "    \n", "    if L1 < 0 or L2 < 0:\n", "        raise ValueError(\n", "            \"计算出的路径长度为负，无法创建弯曲。 \"\n", "            \"请尝试增大 'peak_curvature' 或减小 'final_angle'。\"\n", "        )\n", "\n", "    # --- 构建路径 (修正后的逻辑) ---\n", "    # 1. 创建总路径长度数组\n", "    s_total = np.linspace(0, L1 + L2, npoints)\n", "    k = np.zeros_like(s_total)\n", "\n", "    # 2. 根据s_total中的每个点，计算对应的曲率k\n", "    for i, s in enumerate(s_total):\n", "        if s <= L1:\n", "            # 第一部分: 曲率从 0 上升到 k_peak\n", "            k[i] = (k_peak / 2) * (1 - np.cos(np.pi * s / L1))\n", "        else:\n", "            # 第二部分: 曲率从 k_peak 变化到 k_final\n", "            s_prime = s - L1\n", "            k[i] = (k_peak + k_final) / 2 + (k_peak - k_final) / 2 * np.cos(np.pi * s_prime / L2)\n", "\n", "    # 3. 通过积分计算角度和位置 (使用梯形法则以提高精度)\n", "    theta_cum = np.zeros_like(s_total)\n", "    for i in range(1, npoints):\n", "        theta_cum[i] = theta_cum[i-1] + (k[i] + k[i-1]) * (s_total[i] - s_total[i-1]) / 2\n", "\n", "    x = np.zeros_like(s_total)\n", "    y = np.zeros_like(s_total)\n", "    for i in range(1, npoints):\n", "        ds = s_total[i] - s_total[i-1]\n", "        avg_angle = (theta_cum[i] + theta_cum[i-1]) / 2\n", "        x[i] = x[i-1] + np.cos(avg_angle) * ds\n", "        y[i] = y[i-1] + np.sin(avg_angle) * ds\n", "        \n", "    points = np.column_stack((x, y))\n", "\n", "    # --- 创建 gdsfactory 组件 ---\n", "    c = gf.Component()\n", "    path = gf.Path(points)\n", "    bend = c << gf.path.extrude(path, cross_section=cross_section)\n", "    c.add_ports(bend.ports)\n", "    \n", "    # 打印一些计算出的参数作为参考\n", "    print(\n", "        f\"输入: angle={final_angle}, k_final={k_final}, k_peak={k_peak} -> \"\n", "        f\"计算得出: L1={L1:.2f}um, L2={L2:.2f}um, \"\n", "        f\"最终角度={np.rad2deg(theta_cum[-1]):.2f}deg\"\n", "    )\n", "\n", "    return c\n", "\n", "if __name__ == \"__main__\":\n", "    # --- 示例：比较不同 peak_curvature 的效果 ---\n", "    # 我们的目标是：最终角度45度，最终曲率为-0.1 (半径-10um的顺时针弯曲)\n", "\n", "    # 示例1：使用较高的 peak_curvature (更紧凑，更高损耗)\n", "    c1 = gvc_bend_optimal(\n", "        final_angle=45, final_curvature=-1/100, peak_curvature=1.05/100\n", "    )\n", "\n", "    # 示例2：使用较低的 peak_curvature (更舒展，更低损耗)\n", "    c2 = gvc_bend_optimal(\n", "        final_angle=45, final_curvature=-1/100, peak_curvature=1.05/100\n", "    )\n", "    \n", "    # 将它们放在一起进行比较\n", "    c = gf.Component()\n", "    # ref1 = c_compare << c1\n", "    ref2 = c << c2\n", "    \n", "    # 为了方便比较，将第二个弯曲的起点对齐到第一个的终点\n", "    # ref2.connect(\"o1\", ref1.ports[\"o2\"])\n", "    c2.draw_ports()\n", "    c2.plot()\n", "    c2.show()\n"]}, {"cell_type": "code", "execution_count": 39, "id": "d669640d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def looploop(num_pts=1000):\n", "    \"\"\"Simple limacon looping curve.\"\"\"\n", "    t = np.linspace(-np.pi, 0, num_pts)\n", "    r = 20 + 25 * np.sin(t)\n", "    x = r * np.cos(t)\n", "    y = r * np.sin(t)\n", "    return np.array((x, y)).T\n", "\n", "\n", "# Create the path points\n", "P = gf.Path()\n", "P.append(gf.path.arc(radius=10, angle=90))\n", "P.append(gf.path.straight())\n", "P.append(gf.path.arc(radius=5, angle=-90))\n", "P.append(looploop(num_pts=1000))\n", "<PERSON><PERSON>rotate(-45)\n", "\n", "# Create the crosssection\n", "s0 = gf.Section(width=1, offset=0, layer=(1, 0), port_names=(\"in\", \"out\"))\n", "s1 = gf.Section(width=0.5, offset=2, layer=(2, 0))\n", "s2 = gf.Section(width=0.5, offset=4, layer=(3, 0))\n", "s3 = gf.Section(width=1, offset=0, layer=(4, 0))\n", "X = gf.CrossSection(sections=(s0, s1, s2, s3))\n", "\n", "c = gf.path.extrude(P, X)\n", "c.plot()"]}, {"cell_type": "code", "execution_count": 17, "id": "b1c91c5b", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import gdsfactory as gf\n", "\n", "@gf.cell\n", "def gvc_bend(\n", "    final_angle: float = 45.0,\n", "    final_curvature: float = -0.1,\n", "    peak_curvature: float = 0.2,\n", "    cross_section: gf.typings.CrossSectionSpec = \"strip\",\n", "    npoints: int = 720,\n", ") -> gf.Component:\n", "    \"\"\"General Variable Curvature Bend.\n", "\n", "    Args:\n", "        final_angle: Final angle in degrees.\n", "        final_curvature: Final curvature in 1/μm.\n", "        peak_curvature: Peak curvature in 1/μm.\n", "        cross_section: Cross-section spec.\n", "        npoints: Number of points for discretization.\n", "\n", "    \"\"\"\n", "    if peak_curvature <= 0 or final_curvature >= 0:\n", "        raise ValueError(\"For this example, assume positive peak_curvature and negative final_curvature.\")\n", "    if peak_curvature + final_curvature <= 0:\n", "        raise ValueError(\"peak_curvature must be > -final_curvature.\")\n", "\n", "    theta = np.deg2rad(final_angle)\n", "    k_final = final_curvature\n", "    k_peak = peak_curvature\n", "\n", "    r = -k_final / k_peak + 1\n", "    sp1 = 2 * theta / (k_peak + (k_peak + k_final) * r)\n", "    sp2 = r * sp1\n", "\n", "    # Discretize with trapezoid integration for accuracy\n", "    n1 = npoints // 2\n", "    n2 = npoints - n1  # Ensure total npoints\n", "\n", "    s1 = np.linspace(0, sp1, n1)\n", "    k1 = (k_peak / sp1) * s1 if sp1 > 0 else np.zeros(n1)\n", "    theta_cum1 = np.cumtrapz(k1, s1, initial=0)  # Cumtrapz for precise cumulative integral\n", "\n", "    s2 = np.linspace(0, sp2, n2)\n", "    k2 = k_peak + (k_final - k_peak) / sp2 * s2 if sp2 > 0 else np.zeros(n2)\n", "    theta_cum2 = np.cumtrapz(k2, s2, initial=0) + theta_cum1[-1]\n", "\n", "    s = np.concatenate((s1[:-1], s1[-1] + s2)) if sp1 > 0 and sp2 > 0 else (s1 if sp1 > 0 else s2)\n", "    theta_cum = np.concatenate((theta_cum1[:-1], theta_cum2)) if sp1 > 0 and sp2 > 0 else (theta_cum1 if sp1 > 0 else theta_cum2)\n", "\n", "    # ds for position integration (approximate with diff(s))\n", "    ds = np.diff(s, prepend=0)  # ds[0]=0, but positions start from 0\n", "\n", "    # Positions: integrate cos/sin with trapezoid for better accuracy\n", "    cos_theta = np.cos(theta_cum)\n", "    sin_theta = np.sin(theta_cum)\n", "    x = np.cumtrapz(cos_theta, s, initial=0)\n", "    y = np.cumtrapz(sin_theta, s, initial=0)\n", "\n", "    points = np.column_stack((x, y))\n", "\n", "    p = gf.Path()\n", "    p.points = points\n", "\n", "    c = p.extrude(cross_section=cross_section)\n", "\n", "    # Add ports\n", "    c.add_port(name=\"o1\", center=(0, 0), orientation=0, cross_section=cross_section)\n", "\n", "    final_center = points[-1]\n", "    final_orientation = np.rad2deg(theta_cum[-1])\n", "    c.add_port(name=\"o2\", center=final_center, orientation=final_orientation, cross_section=cross_section)\n", "\n", "    return c\n", "\n", "# Example usage\n", "c = gvc_bend()\n", "c.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "c8b42b9c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cs = gf.cross_section.strip(width=1, layer=(1, 0), radius_min=100.0)\n", "c= gf.components.bend_s(size=(400, 300),\n", "                             npoints=200, cross_section=cs, allow_min_radius_violation=False)\n", "c.draw_ports()\n", "c.plot()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}