@gf.cell
def gvc_bend_optimal(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """
    创建一个理论最优的通用可变曲率弯曲 (C2连续)。

    该弯曲的曲率 k(s) 及其一阶导数 dk/ds 全程连续，
    从而最大限度地减少过渡损耗。

    Args:
        final_angle: 最终的目标角度 (degrees)。
        final_curvature: 最终的目标曲率 (1/μm)。正=逆时针, 负=顺时针。
        peak_curvature: 路径中的峰值曲率 (1/μm)。这是一个正值，
                        用作损耗/尺寸的控制参数。值越小，弯曲越平缓，
                        损耗越低，但尺寸越大。
        cross_section: 波导的横截面。
        npoints: 离散化点数。
    """
    # --- 输入参数验证 ---
    k_final = final_curvature
    k_peak = peak_curvature
    #final_angle=90-final_angle
    if k_peak <= 0:
        raise ValueError("peak_curvature 必须为正数。")
    if np.abs(k_final) >= k_peak:
        raise ValueError(
            f"peak_curvature ({k_peak}) 必须大于等于最终曲率的绝对值 "
            f"abs(final_curvature) ({np.abs(k_final)})."
        )

    # --- 核心逻辑: 根据输入参数反解路径长度 L1 和 L2 ---
    # 我们建立一个模型，其中曲率由两段升余弦函数构成，
    # 并假设两段的最大变化率 dk/ds 大小相等，以获得对称平滑的过渡。
    # 最终可以推导出 L1 和 L2 的解析解：
    
    theta_final_rad = np.deg2rad(final_angle)
    
    # 求解 L1 (从 k=0 到 k=k_peak 的路径长度)
    denominator = (k_peak / 2) + (k_peak + k_final) / 2 * (k_peak - k_final) / k_peak
    if np.isclose(denominator, 0):
        # 避免除以零
        denominator = 1e-9
    L1 = theta_final_rad / denominator

    # 求解 L2 (从 k=k_peak 到 k=k_final 的路径长度)
    L2 = L1 * (k_peak - k_final) / k_peak
    
    if L1 < 0 or L2 < 0:
        raise ValueError(
            "计算出的路径长度为负，无法创建弯曲。 "
            "请尝试增大 'peak_curvature' 或减小 'final_angle'。"
        )

    # --- 构建路径 (修正后的逻辑) ---
    # 1. 创建总路径长度数组
    s_total = np.linspace(0, L1 + L2, npoints)
    k = np.zeros_like(s_total)

    # 2. 根据s_total中的每个点，计算对应的曲率k
    for i, s in enumerate(s_total):
        if s <= L1:
            # 第一部分: 曲率从 0 上升到 k_peak
            k[i] = (k_peak / 2) * (1 - np.cos(np.pi * s / L1))
        else:
            # 第二部分: 曲率从 k_peak 变化到 k_final
            s_prime = s - L1
            k[i] = (k_peak + k_final) / 2 + (k_peak - k_final) / 2 * np.cos(np.pi * s_prime / L2)

    # 3. 通过积分计算角度和位置 (使用梯形法则以提高精度)
    theta_cum = np.zeros_like(s_total)
    for i in range(1, npoints):
        theta_cum[i] = theta_cum[i-1] + (k[i] + k[i-1]) * (s_total[i] - s_total[i-1]) / 2

    x = np.zeros_like(s_total)
    y = np.zeros_like(s_total)
    for i in range(1, npoints):
        ds = s_total[i] - s_total[i-1]
        avg_angle = (theta_cum[i] + theta_cum[i-1]) / 2
        x[i] = x[i-1] + np.cos(avg_angle) * ds
        y[i] = y[i-1] + np.sin(avg_angle) * ds
        
    points = np.column_stack((x, y))

    # --- 创建 gdsfactory 组件 ---
    c = gf.Component()
    path = gf.Path(points)
    bend = c << gf.path.extrude(path, cross_section=cross_section)
    c.add_ports(bend.ports)
    
    # 打印一些计算出的参数作为参考
    # print(
    #     f"输入: angle={final_angle}, k_final={k_final}, k_peak={k_peak} -> "
    #     f"计算得出: L1={L1:.2f}um, L2={L2:.2f}um, "
    #     f"最终角度={np.rad2deg(theta_cum[-1]):.2f}deg"
    # )

    return c