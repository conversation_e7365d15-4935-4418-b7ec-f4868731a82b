# -*- coding: utf-8 -*-
"""
波导模式分析器

用于分析 Lumerical 仿真输出的波导模式特性，包括偏振识别、阶数确定和传播特性计算。
主要用于与 write_sparameters_lumerical_mode 模块配合使用。

功能：
1. 偏振识别：自动判断 TE、TM 或hybrid模式
2. 阶数识别：分析场分布确定模式阶数 (m, n)
3. 传播特性：计算相速度、群速度、群速度色散等参数

依赖库：numpy, scipy
"""

import logging
import numpy as np
from scipy.signal import find_peaks
from scipy.interpolate import UnivariateSpline
from typing import Dict, Tuple, Union, Optional, Any
from dataclasses import dataclass
import matplotlib.pyplot as plt
import time
from pathlib import Path
@dataclass
class ModeAnalysisConfig:
    te_threshold: float = 0.9  # TE判定阈值（Hz能量或横向能量占比）
    tm_threshold: float = 0.9  # TM判定阈值（Ez能量占比）
    min_peak_rel_height: float = 0.1  # 峰值高度相对阈值
    peak_prominence_rel: float = 0.05  # 峰突出度相对阈值
    spline_s: float = 1e-6  # 色散拟合平滑参数
    spline_k: int = 3  # 样条阶数
    enforce_wavelength_in_range: bool = True  # 若目标波长越界是否报错

class WaveguideModeAnalyzer:
    """
    波导模式分析器
    
    用于分析 Lumerical 仿真数据，提取波导模式的关键特性参数
    """
    
    def __init__(self, 
                 e_field_data: Dict[str, np.ndarray], 
                 h_field_data: Dict[str, np.ndarray],
                 neff_data: Dict[str, np.ndarray], 
                 target_wavelength: float,
                 direction:str,
                 config: Optional[ModeAnalysisConfig] = None):
        """
        初始化模式分析器
        
        参数:
            e_field_data: 电场数据字典，包含 'Ex', 'Ey', 'Ez', 'x', 'y'
            h_field_data: 磁场数据字典，包含 'Hx', 'Hy', 'Hz', 'x', 'y' 
            neff_data: 有效折射率数据字典，包含 'neff', 'lambda'
            target_wavelength: 目标分析波长 (米)
            config: 可选的模式分析配置参数
        """
        # 数据验证
        self._validate_input_data(e_field_data, h_field_data, neff_data)
        
        self.e_data = e_field_data
        self.h_data = h_field_data  
        self.neff_data = neff_data
        self.target_wavelength = target_wavelength
        self.c = 299792458.0  # 真空光速 (m/s)
        self.direction=direction
        # 分析结果存储
        self.results = {}
        self.config = config or ModeAnalysisConfig()
        
    def _validate_input_data(self, e_data: Dict, h_data: Dict, neff_data: Dict) -> None:
        """验证输入数据格式和完整性"""
        required_e_keys = ['Ex', 'Ey', 'Ez', 'y', 'z']
        required_h_keys = ['Hx', 'Hy', 'Hz', 'y', 'z']
        required_neff_keys = ['neff', 'lambda']
        
        # 检查必需字段
        for key in required_e_keys:
            if key not in e_data:
                raise ValueError(f"电场数据缺少必需字段: {key}")
                
        for key in required_h_keys:
            if key not in h_data:
                raise ValueError(f"磁场数据缺少必需字段: {key}")
                
        for key in required_neff_keys:
            if key not in neff_data:
                raise ValueError(f"有效折射率数据缺少必需字段: {key}")
        
        # 检查数据形状一致性
        field_shape = e_data['Ex'].shape
        for field in ['Ey', 'Ez']:
            if e_data[field].shape != field_shape:
                raise ValueError(f"电场分量 {field} 形状不一致")
                
        for field in ['Hx', 'Hy', 'Hz']:
            if h_data[field].shape != field_shape:
                raise ValueError(f"磁场分量 {field} 形状不一致")
    
    def identify_polarization(self) -> Dict[str, Union[str, float]]:
        """
        识别模式偏振特性
        
        返回:
            包含偏振类型和偏振比例的字典
        """
        # 新算法：横向 Et = Ex^2+Ey^2；纵向 El = Ez^2
        Ex, Ey, Ez = self.e_data['Ex'], self.e_data['Ey'], self.e_data['Ez']
        direction=self.direction
        # 小噪声截断
        eps = 1e-18

        Hx, Hy, Hz = self.h_data['Hx'], self.h_data['Hy'], self.h_data['Hz']
        # 小噪声截断
        eps = 1e-18

        # 计算 Ex/Ey 横向能量比例
        Ez_energy = float(np.sum(np.abs(Ez)**2))
        Ey_energy = float(np.sum(np.abs(Ey)**2))
        Ex_energy = float(np.sum(np.abs(Ex)**2))
        if direction=='y-axis':
            Et_energy = float(np.sum(np.abs(Ex)**2 + np.abs(Ez)**2))
            El_energy = float(np.sum(np.abs(Ey)**2))
            Ht_energy = float(np.sum(np.abs(Hx)**2 + np.abs(Hz)**2))
            Hl_energy = float(np.sum(np.abs(Hy)**2))
            E_total = Et_energy + El_energy + eps
            epara_ratio = Ex_energy / E_total
            ever_ratio = Ez_energy / E_total
        else:
            Et_energy = float(np.sum(np.abs(Ez)**2 + np.abs(Ey)**2))
            El_energy = float(np.sum(np.abs(Ex)**2))
            Ht_energy = float(np.sum(np.abs(Hz)**2 + np.abs(Hy)**2))
            Hl_energy = float(np.sum(np.abs(Hx)**2))
            E_total = Et_energy + El_energy + eps
            epara_ratio = Ey_energy / E_total
            ever_ratio = Ez_energy / E_total

        et_ratio = Et_energy / E_total
        el_ratio = El_energy / E_total
        H_total = Ht_energy + Hl_energy + eps
        Ht_ratio = Ht_energy / H_total
        Hl_ratio = Hl_energy / H_total


        # 主横向分量判断：Ey vs Ex
        te_thr = self.config.te_threshold
        tm_thr = self.config.tm_threshold
        if et_ratio >= te_thr:
            pol = 'TE'
        elif Ht_ratio >= tm_thr:
            pol = 'TM'
        else:
            pol = 'hybrid mode'
        info = {
            'type': pol,
            'et_ratio': et_ratio,
            'el_ratio': el_ratio,
            'Ht_ratio': Ht_ratio,
            'Hl_ratio': Hl_ratio,
            'epara_ratio': epara_ratio, 
            'ever_ratio': ever_ratio,
            #'hybrid_parameter': 
        }
        self.results.update(info)
        #print(info)
        return info

    def determine_mode_order(self) -> Tuple[int, int]:
        """
        确定模式阶数 (m, n)
        
        返回:
            模式阶数元组 (m, n)
        """
        pol = self.results.get('type') or self.identify_polarization()['type']
        Ex, Ey, Ez = self.e_data['Ex'], self.e_data['Ey'], self.e_data['Ez']
        
        if pol == 'TE':
            # 使用横向合成幅度，提高稳健性
            field = np.sqrt(np.abs(Ex)**2 + np.abs(Ey)**2+np.abs(Ez)**2)
        elif pol == 'TM':
            Hx,Hy,Hz=self.h_data['Hx'],self.h_data['Hy'],self.h_data['Hz']
            field = np.sqrt(np.abs(Hx)**2 + np.abs(Hy)**2 + np.abs(Hz)**2)
        else:
            field = np.sqrt(np.abs(Ex)**2 + np.abs(Ey)**2 + np.abs(Ez)**2)
        # 归一化与轻微平滑(可选：此处简单 clip)
        mval = np.max(field) + 1e-18
        field_norm = field / mval
        # 最大值索引
        max_idx = np.unravel_index(np.argmax(field_norm), field_norm.shape)
        y_profile = field_norm[:, max_idx[1]]
        z_profile = field_norm[max_idx[0], :]
        rel = self.config.min_peak_rel_height
        prominence_rel = self.config.peak_prominence_rel
        peaks_y, _ = find_peaks(y_profile, height=rel, prominence=prominence_rel)
        peaks_z, _ = find_peaks(z_profile, height=rel, prominence=prominence_rel)
        m_ord = max(0, len(peaks_y) - 1)
        n_ord = max(0, len(peaks_z) - 1)
        self.results['mode_order'] = (m_ord, n_ord)
        return (m_ord, n_ord)
    
    def calculate_propagation_properties(self) -> Dict[str, Any]:
        """
        计算传播特性参数
        
        返回:
            包含各种传播参数和绘图数据的字典
        """
        # 提取并排序色散数据
        wavelengths = self.neff_data['lambda'].flatten()
        neff_values = self.neff_data['neff'].flatten()
        
        # 确保neff数据为实数（取实部，忽略损耗）
        if np.iscomplexobj(neff_values):
            logger = logging.getLogger(__name__)
            logger.warning(f"警告: neff数据包含复数，将使用实部进行色散分析")
            neff_values = np.real(neff_values)
        
        # 按波长排序
        sort_indices = np.argsort(wavelengths)
        wavelengths_sorted = wavelengths[sort_indices] * 1e6  # 转换为微米用于计算
        neff_sorted = neff_values[sort_indices]
        
        # 确保数据类型为float64，避免数值精度问题
        wavelengths_sorted = wavelengths_sorted.astype(np.float64)
        neff_sorted = neff_sorted.astype(np.float64)
        
        cfg = self.config
        target_wl_um = self.target_wavelength   # 目标波长转换为微米
        
        if cfg.enforce_wavelength_in_range:
            if not (wavelengths_sorted.min() <= target_wl_um <= wavelengths_sorted.max()):
                raise ValueError("目标波长超出neff数据范围")
        
        # 准备绘图数据
        plot_data = {
            'wavelengths_um': wavelengths_sorted,
            'neff_original': neff_sorted,
            'spline_function': None,
            'smooth_wavelengths': None,
            'smooth_neff': None
        }
        
        # 检查数据点数量，如果太少则使用线性插值
        if len(wavelengths_sorted) < 4:
            # 数据点太少，使用线性插值
            neff = float(np.interp(target_wl_um, wavelengths_sorted, neff_sorted))
            
            # 计算简单的数值导数
            if len(wavelengths_sorted) >= 2:
                # 使用有限差分
                dwl = wavelengths_sorted[-1] - wavelengths_sorted[0]
                dneff = neff_sorted[-1] - neff_sorted[0]
                dneff_dlambda = dneff / dwl if dwl > 0 else 0.0
                d2neff_dlambda2 = 0.0  # 二阶导数设为0
            else:
                dneff_dlambda = 0.0
                d2neff_dlambda2 = 0.0
                
            # 为绘图准备线性插值数据
            plot_data['smooth_wavelengths'] = np.linspace(wavelengths_sorted.min(), wavelengths_sorted.max(), 100)
            plot_data['smooth_neff'] = np.interp(plot_data['smooth_wavelengths'], wavelengths_sorted, neff_sorted)
        else:
            # 数据点足够，使用样条插值
            # 添加数据验证，确保没有NaN或无穷大值
            valid_mask = np.isfinite(wavelengths_sorted) & np.isfinite(neff_sorted)
            if not np.all(valid_mask):
                logger = logging.getLogger(__name__)
                logger.warning(f"警告: 发现{np.sum(~valid_mask)}个无效数据点，将被过滤")
                wavelengths_sorted = wavelengths_sorted[valid_mask]
                neff_sorted = neff_sorted[valid_mask]
            
            try:
                spline = UnivariateSpline(wavelengths_sorted, neff_sorted, 
                                        k=min(cfg.spline_k, len(wavelengths_sorted)-1), 
                                        s=cfg.spline_s)
            except Exception as e:
                logger = logging.getLogger(__name__)
                logger.error(f"样条插值失败: {e}")
                logger.error(f"wavelengths_sorted类型: {wavelengths_sorted.dtype}, 范围: [{wavelengths_sorted.min():.6f}, {wavelengths_sorted.max():.6f}]")
                logger.error(f"neff_sorted类型: {neff_sorted.dtype}, 范围: [{neff_sorted.min():.6f}, {neff_sorted.max():.6f}]")
                # 降级到线性插值
                neff = float(np.interp(target_wl_um, wavelengths_sorted, neff_sorted))
                dneff_dlambda = 0.0
                d2neff_dlambda2 = 0.0
                plot_data['smooth_wavelengths'] = np.linspace(wavelengths_sorted.min(), wavelengths_sorted.max(), 100)
                plot_data['smooth_neff'] = np.interp(plot_data['smooth_wavelengths'], wavelengths_sorted, neff_sorted)
            else:
                # 计算一阶和二阶导数
                dneff_dlambda_spline = spline.derivative(n=1)
                d2neff_dlambda2_spline = spline.derivative(n=2)
                
                # 在目标波长处计算各参数
                neff = float(spline(target_wl_um))
                dneff_dlambda = float(dneff_dlambda_spline(target_wl_um))
                d2neff_dlambda2 = float(d2neff_dlambda2_spline(target_wl_um))
                
                # 为绘图准备样条插值数据
                plot_data['spline_function'] = spline
                plot_data['smooth_wavelengths'] = np.linspace(wavelengths_sorted.min(), wavelengths_sorted.max(), 100)
                plot_data['smooth_neff'] = spline(plot_data['smooth_wavelengths'])
        
        wl = self.target_wavelength
        
        # 传播特性计算
        group_index = neff - target_wl_um * dneff_dlambda  # 群折射率
        
        # 群速度色散参数 (转换为常用单位 ps/(nm·km))
        gvd_si = -(wl / self.c) * d2neff_dlambda2  # SI单位
        gvd_conventional = gvd_si * 1e12  # ps/(nm·km)
        
        propagation_props = {
            'effective_index': neff,
            'group_index': group_index, 
            'gvd_parameter': gvd_conventional,
            'wavelength': wl,
            'plot_data': plot_data  # 添加绘图所需数据
        }
        
        self.results.update(propagation_props)
        return propagation_props
    
    def analyze_mode(self) -> Dict[str, Any]:
        """
        执行完整的模式分析
        
        返回:
            包含所有分析结果的字典
        """
        try:
            # 步骤1：偏振识别
            polarization_info = self.identify_polarization()
            
            # 步骤2：阶数确定
            mode_order = self.determine_mode_order()
            
            # 步骤3：传播特性计算
            propagation_props = self.calculate_propagation_properties()
            
            # 生成模式名称
            pol_type = polarization_info['type']
            m, n = mode_order
            mode_name = f"{pol_type}_{m}{n}"
            
            # 汇总结果
            complete_results = {
                'mode_name': mode_name,
                'polarization': polarization_info,
                'mode_order': mode_order,
                'propagation': propagation_props,
                'analysis_wavelength': self.target_wavelength,
                'analysis_success': True
            }
            
            self.results.update(complete_results)
            return self.results
            
        except Exception as e:
            error_result = {
                'analysis_success': False,
                'error_message': str(e),
                'analysis_wavelength': self.target_wavelength
            }
            return error_result
    
    def get_mode_summary(self) -> str:
        """
        生成模式分析摘要报告
        
        返回:
            格式化的分析报告字符串
        """
        if not self.results or not self.results.get('analysis_success', False):
            return "模式分析尚未完成或失败"
            
        report = f"""
=== 波导模式分析报告 ===
分析波长: {self.results['analysis_wavelength']*1e6:.3f} μm
模式名称: {self.results['mode_name']}

偏振特性:
- 类型: {self.results['polarization']['type']}
- TE成分: {self.results['polarization']['et_ratio']:.3f}
- TM成分: {self.results['polarization']['el_ratio']:.3f}

模式阶数: ({self.results['mode_order'][0]}, {self.results['mode_order'][1]})


        """
        return report.strip()


def analyze_lumerical_mode(e_field_data: Dict[str, np.ndarray],
                          h_field_data: Dict[str, np.ndarray], 
                          neff_data: Dict[str, np.ndarray],
                          target_wavelength: float = 1.55e-6,
                          direction:str='x-axis',) -> Dict[str, Any]:
    """
    便捷函数：分析 Lumerical 模式数据
    
    参数:
        e_field_data: 电场数据
        h_field_data: 磁场数据  
        neff_data: 有效折射率数据
        target_wavelength: 分析波长，默认1.55μm
        
    返回:
        完整的分析结果字典
    """
    analyzer = WaveguideModeAnalyzer(e_field_data, h_field_data, neff_data, target_wavelength,direction)
    return analyzer.analyze_mode()

def set_port_mode_solver_data(
    session,
    port_name: str,
    target_wavelength: float,
    mode_number: int = 0,
    selected_mode_type: str = None,
    port_type:str=None,
    injection:str=None,
    output_dir: Optional[Path] = None,
) -> Dict[str, Any]:
    """
    使用端口的内置 eigenmode 求解结果 (mode solver) 获取模式数据（不触发 3D FDTD 再运行）。

    约定:
        - 已存在端口/模式监视器 (mode expansion / eigenmode) 并求解完成。
        - Lumerical 中通过 getresult(port, 'neff') 和 getresult(port, 'mode profiles') 访问。

    参数:
        session: Lumerical 会话对象 (FDTD / MODE)
        port_name: 端口或模式监视器名称
        target_wavelength: 目标波长 (m)
        mode_number: 总模式数量
        selected_mode_type: 要返回的模式类型 (如 'TE00', 'TM01' 等，默认返回所有模式)

    返回:
        dict: 包含所有模式分析结果的字典，或选择的特定模式类型结果
    """
    #设置波长范围和模式数，这里是用于色散分析的
    port_obj = port_name
    session.setglobalsource("wavelength start", (target_wavelength-0.01)*10**(-6))
    session.setglobalsource("wavelength stop", (target_wavelength+0.01)*10**(-6))
    session.setnamed(port_obj, "frequency dependent profile", True)
    session.setnamed(port_obj, "number of field profile samples", 10)
    session.setnamed(port_obj, "mode selection", "user select")
    session.setnamed(port_obj, "selected mode numbers", np.arange(1, mode_number + 1).reshape(1, -1))
    # 获取有效折射率数据和模式分布
    # Select the port explicitly
    session.select(port_obj)
    session.updateportmodes()
    start_time = time.time()
    # Update the mode profiles (no arguments to update existing/selected modes)
    # Poll until neff stabilizes
    max_wait = 2000  # Maximum wait time in seconds
    poll_interval = 1  # Small interval
    tol = 1e-6  # Tolerance for neff stability (adjust based on your precision needs)
    prev_neff = None
    while True:
        try:
            neff_data = session.getresult(port_obj, "neff")
            time.sleep(poll_interval)
            current_neff = neff_data['neff']  # Assuming 'neff' is an array or scalar
        
            if prev_neff is not None:
             # Compare current to previous (use np.allclose for arrays, or simple abs diff for scalars)
             if np.allclose(current_neff, prev_neff, atol=tol):
                  break  # Stabilized
                  
            prev_neff = current_neff
           # print(neff_data['neff'],prev_neff)
           # print('-'*100)
        except Exception:
            # If getresult fails initially (no data yet), continue polling
            pass
    
        if time.time() - start_time > max_wait:
         raise RuntimeError(f"Timeout waiting for stable mode results {port_obj}")
    
        time.sleep(poll_interval)
    try:
        neff_data = session.getresult(port_obj, "neff")
        mode_profile = session.getresult(port_obj, "mode profiles")
    except Exception as e:
        raise RuntimeError(f"无法获取模式结果 {port_obj}: {e}")
    lambda_data=neff_data['lambda']
    neff_values=neff_data['neff']
    z=mode_profile['z']
    if injection=='y-axis':
        y=mode_profile['x']
    else:
        y=mode_profile['y']
    
    # 分析每个模式并画图
   
    num_modes = neff_values.shape[1] if neff_values.ndim > 1 else 1
    fig, axes = plt.subplots(2, num_modes, figsize=(4*num_modes, 8))
    if num_modes == 1:
        axes = axes.reshape(-1, 1)
    mode_results = {}
    for i in range(1, num_modes + 1):
        e_field=mode_profile['E'+str(i)]
        h_field=mode_profile['H'+str(i)]
        
        # 提取电场和磁场分量（取中间频率点）
        freq_idx = e_field.shape[3] // 2  # 中间频率索引
        if injection== 'y-axis':
            ex = np.abs(e_field[:, 0, :, freq_idx, 0])  # Ex分量
            ey = np.abs(e_field[:, 0, :, freq_idx, 1])  # Ey分量
            ez = np.abs(e_field[:, 0, :, freq_idx, 2])  # Ez分量
            hx = np.abs(h_field[:, 0, :, freq_idx, 0])  # Hx分量
            hy = np.abs(h_field[:, 0, :, freq_idx, 1])  # Hy分量
            hz = np.abs(h_field[:, 0, :, freq_idx, 2])  # Hz分量
        else:
            ex = np.abs(e_field[0, :, :, freq_idx, 0])  # Ex分量
            ey = np.abs(e_field[0, :, :, freq_idx, 1])  # Ey分量
            ez = np.abs(e_field[0, :, :, freq_idx, 2])  # Ez分量
            hx = np.abs(h_field[0, :, :, freq_idx, 0])  # Hx分量
            hy = np.abs(h_field[0, :, :, freq_idx, 1])  # Hy分量
            hz = np.abs(h_field[0, :, :, freq_idx, 2])  # Hz分量
        E=np.sqrt(np.abs(ex)**2+np.abs(ey)**2+np.abs(ez)**2)
        H=np.sqrt(np.abs(hx)**2+np.abs(hy)**2+np.abs(hz)**2)

        # 分析模式特性
        if injection== 'y-axis':
            mode_data = {
                'e_field': {'Ex': e_field[:, 0, :, freq_idx, 0], 'Ey': e_field[:, 0, :, freq_idx, 1], 'Ez':e_field[:, 0, :, freq_idx, 2], 'y': y, 'z': z},
                'h_field': {'Hx': h_field[:, 0, :, freq_idx, 0], 'Hy': h_field[:, 0, :, freq_idx, 1], 'Hz': h_field[:, 0, :, freq_idx, 2], 'y': y, 'z': z},
                'neff': {'lambda': lambda_data, 'neff': neff_values[:, i-1:i] if neff_values.ndim > 1 else neff_values}
            }
        else:
            mode_data = {
                'e_field': {'Ex': e_field[0,:,:,0,0], 'Ey': e_field[0,:,:,0,1], 'Ez': e_field[0,:,:,0,2], 'y': y, 'z': z},
                'h_field': {'Hx': h_field[0,:,:,0,0], 'Hy': h_field[0,:,:,0,1], 'Hz': h_field[0,:,:,0,2], 'y': y, 'z': z},
                'neff': {'lambda': lambda_data, 'neff': neff_values[:, i-1:i] if neff_values.ndim > 1 else neff_values}
            }
        
        try:
            analysis_result = analyze_lumerical_mode(mode_data['e_field'], mode_data['h_field'],
                                                   mode_data['neff'], target_wavelength,direction=injection)
            logger = logging.getLogger(__name__)
            logger.info(f"模式 {i}: {analysis_result.get('mode_name', 'N/A')}, 模场分布: {analysis_result.get('polarization', {})}")
            mode_results[i] = analysis_result
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"模式 {i} 分析失败: {e}")
            mode_results[i] = {'analysis_success': False, 'error_message': str(e)}

        # 画模式i的E和H分布
        col_idx = i - 1
        im_E = axes[0, col_idx].imshow(
            E.T,
            extent=[y.min()*1e6, y.max()*1e6, z.min()*1e6, z.max()*1e6],
            aspect='auto',
            origin='lower',
            cmap='jet'
        )
        axes[0, col_idx].set_title(f'Mode {i} |E|,et_ratio:{analysis_result.get('polarization', {}).get('et_ratio','N/A')},epara_ratio:{analysis_result.get('polarization', {}).get('epara_ratio','N/A')}')
        axes[0, col_idx].set_xlabel('y (μm)')
        axes[0, col_idx].set_ylabel('z (μm)')
        fig.colorbar(im_E, ax=axes[0, col_idx])

        im_H = axes[1, col_idx].imshow(
            H.T,
            extent=[y.min()*1e6, y.max()*1e6, z.min()*1e6, z.max()*1e6],
            aspect='auto',
            origin='lower',
            cmap='jet'
        )
        axes[1, col_idx].set_title(f'Mode {i} |H|,ht_ratio:{analysis_result.get('polarization', {}).get('Ht_ratio','N/A')},ever_ratio:{analysis_result.get('polarization', {}).get('ever_ratio','N/A')}')
        axes[1, col_idx].set_xlabel('y (μm)')
        axes[1, col_idx].set_ylabel('z (μm)')
        fig.colorbar(im_H, ax=axes[1, col_idx])


    plt.tight_layout()

    # Save the mode field plots with descriptive naming
    if output_dir:
        timestamp = int(time.time() * 1000000) % 1000000  # 6-digit timestamp
        wl_info = f"wl{target_wavelength*1e6:.2f}um"
        mode_fields_filename = f"mode_fields_{port_name.replace('::', '_')}_{wl_info}_modes{num_modes}_{timestamp}.png"
        mode_fields_path = Path(output_dir) / mode_fields_filename
        plt.savefig(mode_fields_path, dpi=300, bbox_inches='tight')
        logger = logging.getLogger(__name__)
        logger.info(f"Mode field plots saved to: {mode_fields_path}")

    plt.show(block=False)  # 非阻塞显示，不需要用户关闭图形界面

    # 创建新的neff拟合图 - 所有模式在同一图中，利用已计算的数据避免重复
    fig_neff, ax_neff = plt.subplots(1, 1, figsize=(10, 6))
    colors = plt.cm.tab10(np.linspace(0, 1, num_modes))  # 为每个模式分配颜色
    
    for i in range(1, num_modes + 1):
        if i in mode_results and mode_results[i].get('analysis_success', False):
            prop_data = mode_results[i].get('propagation', {})
            plot_data = prop_data.get('plot_data', {})
            mode_name = mode_results[i].get('mode_name', f'Mode{i}')
            ng = prop_data.get('group_index', None)
            gvd = prop_data.get('gvd_parameter', None)
            # 获取模式类型
            pol_type = mode_results[i].get('polarization', {}).get('type', 'N/A')
            # label内容：模式编号、类型、群折射率、GVD
            try:
                label = f"Mode {i} | {pol_type} | ng={ng:.4f} | GVD={gvd:.2f}"
            except Exception:
                label = f"Mode {i} | {pol_type}"
            if plot_data:
                smooth_wl = plot_data.get('smooth_wavelengths')
                smooth_neff = plot_data.get('smooth_neff')
                original_wl = plot_data.get('wavelengths_um')
                original_neff = plot_data.get('neff_original')
                if smooth_wl is not None and smooth_neff is not None:
                    ax_neff.plot(smooth_wl, smooth_neff, '-', color=colors[i-1], linewidth=2, label=label)
                if original_wl is not None and original_neff is not None:
                    ax_neff.plot(original_wl, original_neff, 'o', color=colors[i-1], markersize=4)
                # 目标波长点
                target_wl_um = target_wavelength 
                neff_at_target = prop_data.get('effective_index')
                if neff_at_target is not None:
                    ax_neff.plot(target_wl_um, neff_at_target, 's', color=colors[i-1], markersize=8, markerfacecolor='white', markeredgewidth=2)
    ax_neff.set_xlabel('Wavelength (μm)')
    ax_neff.set_ylabel('Effective Index (neff)')
    ax_neff.set_title('Mode Effective Index vs Wavelength')
    ax_neff.grid(True, alpha=0.3)
    ax_neff.legend()
    # 在目标波长处添加垂直线
    plt.tight_layout()

    # Save the neff plot with descriptive naming
    if output_dir:
        timestamp = int(time.time() * 1000000) % 1000000  # 6-digit timestamp
        wl_info = f"wl{target_wavelength*1e6:.2f}um"
        neff_filename = f"neff_vs_wavelength_{port_name.replace('::', '_')}_{wl_info}_modes{num_modes}_{timestamp}.png"
        neff_path = Path(output_dir) / neff_filename
        plt.savefig(neff_path, dpi=300, bbox_inches='tight')
        logger = logging.getLogger(__name__)
        logger.info(f"Neff vs wavelength plot saved to: {neff_path}")

    plt.show(block=False)
    session.setnamed(port_obj, "frequency dependent profile", False)
    session.setnamed(port_obj, "mode selection", "user select")
    if selected_mode_type and port_type=='Forward':
        logger = logging.getLogger(__name__)
        logger.info(f"\n查找模式类型: {selected_mode_type}")
        found_mode = None
        for mode_num, result in mode_results.items():
            if result.get('analysis_success', False):
                mode_name = result.get('mode_name', '')
                if mode_name == selected_mode_type:
                    found_mode = result
                    logger.info(f"找到匹配的模式 {mode_num}: {mode_name}")
                    session.setnamed(port_obj, "selected mode numbers", mode_num)
                    break
        if found_mode:
            return found_mode
        else:
            logger.warning(f"未找到模式类型 {selected_mode_type}，返回所有模式结果")
            return mode_results
    else:
        # 没有指定模式类型，返回所有模式结果
        session.setnamed(port_obj, "selected mode numbers", np.arange(1, mode_number + 1).reshape(1, -1))
        return mode_results
    
def get_port_mode_solver_data(
    session,
    port_name: str,
    injection:str,
    output_dir: Optional[Path] = None,
) -> Dict[str, Any]:
    port_obj = port_name
    # 获取有效折射率数据和模式分布
    # Select the port explicitly
    session.select(port_obj)
    start_time = time.time()
    # Update the mode profiles (no arguments to update existing/selected modes)
    # Poll until neff stabilizes
    max_wait = 10  # Maximum wait time in seconds
    poll_interval = 1  # Small interval
    tol = 1e-6  # Tolerance for neff stability (adjust based on your precision needs)
    prev_neff = None
    while True:
        try:
            neff_data = session.getresult(port_obj, "neff")
            time.sleep(poll_interval)
            S = session.getresult(port_obj, "S")
            mode_profile_E = session.getresult(port_obj, "E")
            mode_profile_H = session.getresult(port_obj, "H")
            mode_profile_T = session.getresult(port_obj, "T")
            current_neff = neff_data['neff']  # Assuming 'neff' is an array or scalar
        
            if prev_neff is not None:
             # Compare current to previous (use np.allclose for arrays, or simple abs diff for scalars)
             if np.allclose(current_neff, prev_neff, atol=tol):
                  break  # Stabilized
            prev_neff = current_neff
           # print(neff_data['neff'],prev_neff)
           # print('-'*100)
        except Exception as e:
            raise RuntimeError(f"无法获取模式结果 {port_obj}: {e}")
    
        if time.time() - start_time > max_wait:
         raise RuntimeError(f"Timeout waiting for stable mode results {port_obj}")
    
        time.sleep(poll_interval)
    # 提取坐标和场数据
    if injection=='y-axis':
        y = mode_profile_E['x']
    else:
        y = mode_profile_E['y']
    z = mode_profile_E['z']
    E_field = mode_profile_E['E']  # shape: (1, y_points, z_points, 1, 3)
    H_field = mode_profile_H['H']  # shape: (1, y_points, z_points, 1, 3)
    # 计算电场和磁场幅度
    if injection=='y-axis':
        Ex = E_field[:, 0, :, 0, 0]
        Ey = E_field[:, 0, :, 0, 1] 
        Ez = E_field[:, 0, :, 0, 2]
        Hx = H_field[:, 0, :, 0, 0]
        Hy = H_field[:, 0, :, 0, 1]
        Hz = H_field[:, 0, :, 0, 2]
    else:
        Ex = E_field[0, :, :, 0, 0]
        Ey = E_field[0, :, :, 0, 1] 
        Ez = E_field[0, :, :, 0, 2]
        Hx = H_field[0, :, :, 0, 0]
        Hy = H_field[0, :, :, 0, 1]
        Hz = H_field[0, :, :, 0, 2]
    E_magnitude = np.sqrt(np.abs(Ex)**2 + np.abs(Ey)**2 + np.abs(Ez)**2)
    H_magnitude = np.sqrt(np.abs(Hx)**2 + np.abs(Hy)**2 + np.abs(Hz)**2)
    
    # 创建图形 - 2x2布局：E场、H场、S曲线、透射率
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 电场图
    im_E = axes[0, 0].imshow(
        E_magnitude.T,
        extent=[y.min()*1e6, y.max()*1e6, z.min()*1e6, z.max()*1e6],
        aspect='auto',
        origin='lower',
        cmap='jet'
    )
    axes[0, 0].set_title('Electric Field |E|')
    axes[0, 0].set_xlabel('y (μm)')
    axes[0, 0].set_ylabel('z (μm)')
    fig.colorbar(im_E, ax=axes[0, 0])
    
    # 磁场图
    im_H = axes[0, 1].imshow(
        H_magnitude.T,
        extent=[y.min()*1e6, y.max()*1e6, z.min()*1e6, z.max()*1e6],
        aspect='auto',
        origin='lower',
        cmap='jet'
    )
    axes[0, 1].set_title('Magnetic Field |H|')
    axes[0, 1].set_xlabel('y (μm)')
    axes[0, 1].set_ylabel('z (μm)')
    fig.colorbar(im_H, ax=axes[0, 1])
    
    # S参数曲线
    mode_numbers = S['n'].flatten()
    S_values = S['S']
    if S_values.ndim > 1:
        S_magnitude = np.abs(S_values[0, :])  # 取第一行数据
    else:
        S_magnitude = np.abs(S_values)
    
    axes[1, 0].plot(mode_numbers, S_magnitude, 'b-o', linewidth=2, markersize=4)
    axes[1, 0].set_xlabel('Mode Number')
    axes[1, 0].set_ylabel('|S| (Transmission)')
    axes[1, 0].set_title('S-parameter vs Mode Number')
    axes[1, 0].grid(True)
    
    # 透射率显示
    T_value = mode_profile_T['T'][0] if hasattr(mode_profile_T['T'], '__len__') else mode_profile_T['T']
    axes[1, 1].text(0.5, 0.5, f'T = {T_value:.6f}', 
                    fontsize=16, ha='center', va='center',
                    transform=axes[1, 1].transAxes,
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    axes[1, 1].set_title('Transmission Coefficient')
    axes[1, 1].set_xlim(0, 1)
    axes[1, 1].set_ylim(0, 1)
    axes[1, 1].set_xticks([])
    axes[1, 1].set_yticks([])
    
    plt.tight_layout()

    # Save the plot with descriptive naming
    if output_dir:
        timestamp = int(time.time() * 1000000) % 1000000  # 6-digit timestamp
        # Extract transmission value for filename
        T_value = mode_profile_T['T'][0] if hasattr(mode_profile_T['T'], '__len__') else mode_profile_T['T']
        T_info = f"T{T_value:.4f}".replace('.', 'p')  # Replace . with p for filename compatibility
        results_filename = f"port_results_{port_name.replace('::', '_')}_{T_info}_{timestamp}.png"
        results_path = Path(output_dir) / results_filename
        plt.savefig(results_path, dpi=300, bbox_inches='tight')
        logger = logging.getLogger(__name__)
        logger.info(f"Port results plot saved to: {results_path}")

    plt.show(block=False)

    return S,mode_profile_T
    
   



def analyze_port_modes(session, port_names: list, 
                      target_wavelength: float = 1.55e-6,
                      mode_index: int = 0) -> Dict[str, Dict[str, Any]]:
    """
    批量分析多个端口的模式特性
    
    参数:
        session: Lumerical 会话对象
        port_names: 端口名称列表
        target_wavelength: 分析波长
        mode_index: 要分析的模式索引（从0开始）
        
    返回:
        每个端口的分析结果字典
    """
    results = {}
    
    for port_name in port_names:
        try:
            # 提取模式数据
            # 使用基于 mode solver 的轻量级提取
            mode_data = get_port_mode_solver_data(
                session, port_name, target_wavelength, mode_index
            )
            
            # 执行模式分析
            analysis_result = analyze_lumerical_mode(
                mode_data['e_field'],
                mode_data['h_field'], 
                mode_data['neff'],
                target_wavelength
            )
            
            results[port_name] = analysis_result
            
        except Exception as e:
            results[port_name] = {
                'analysis_success': False,
                'error_message': f"端口 {port_name} 分析失败: {str(e)}"
            }
    
    return results


def generate_mode_report(analysis_results: Dict[str, Dict[str, Any]], 
                        output_file: Optional[str] = None) -> str:
    """
    生成多端口模式分析报告
    
    参数:
        analysis_results: 多端口分析结果
        output_file: 可选的输出文件路径
        
    返回:
        格式化的报告字符串
    """
    report_lines = [
        "=" * 80,
        "波导器件模式分析报告",
        "=" * 80,
        f"生成时间: {np.datetime64('now', 's')}",
        ""
    ]
    
    for port_name, result in analysis_results.items():
        report_lines.extend([
            f"端口: {port_name}",
            "-" * 40
        ])
        
        if result.get('analysis_success', False):
            report_lines.extend([
                f"模式名称: {result['mode_name']}",
                f"偏振类型: {result['polarization']['type']}",
                f"模式阶数: {result['mode_order']}",
                f"有效折射率: {result['propagation']['effective_index']:.6f}",
                f"群折射率: {result['propagation']['group_index']:.6f}",
                f"群速度色散: {result['propagation']['gvd_parameter']:.3f} ps/(nm·km)",
                ""
            ])
        else:
            report_lines.extend([
                f"分析失败: {result.get('error_message', '未知错误')}",
                ""
            ])
    
    report_text = "\n".join(report_lines)
    
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_text)
    
    return report_text


if __name__ == '__main__':
    """
    示例用法演示
    """
    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("波导模式分析器演示")
    logger.info("=" * 60)
    
    # 创建示例数据（模拟 TE 基模）
    x = np.linspace(-1.5e-6, 1.5e-6, 100)
    y = np.linspace(-1.5e-6, 1.5e-6, 100)
    xx, yy = np.meshgrid(x, y)
    
    # 高斯型场分布
    width, height = 0.45e-6, 0.22e-6
    field_profile = np.exp(-((xx / (width/2))**2) - ((yy / (height/2))**2))
    
    # 构造电场数据（TE模式：主要是Ey分量）
    e_field_data = {
        'Ex': field_profile * 0.01,
        'Ey': field_profile,  
        'Ez': field_profile * 0.05,
        'x': x,
        'y': y
    }
    
    # 构造磁场数据（TE模式：主要是Hx分量）
    h_field_data = {
        'Hx': field_profile,
        'Hy': field_profile * 0.01,
        'Hz': field_profile * 0.1,
        'x': x, 
        'y': y
    }
    
    # 色散数据
    wavelengths = np.linspace(1.5e-6, 1.6e-6, 21).reshape(-1, 1)
    neff_values = (2.4 - 1.5 * (wavelengths - 1.5e-6) / 0.1e-6).reshape(-1, 1)
    
    neff_data = {
        'lambda': wavelengths,
        'neff': neff_values
    }
    
    # 执行分析
    results = analyze_lumerical_mode(e_field_data, h_field_data, neff_data, 1.55e-6)
    
    # 显示结果
    if results['analysis_success']:
        analyzer = WaveguideModeAnalyzer(e_field_data, h_field_data, neff_data, 1.55e-6, 'x-axis')
        analyzer.results = results
        logger.info(analyzer.get_mode_summary())
    else:
        logger.error(f"分析失败: {results['error_message']}")

    logger.info("\n演示完成")
