import gdsfactory as gf
import numpy as np
@gf.cell
def create_ring_electrode_with_teeth(
    center_x: float = 0,           # 圆心X坐标
    center_y: float = 0,           # 圆心Y坐标
    inner_radius: float = 117,      # 内圆半径（齿的内端）
    outer_radius: float = 120,     # 外圆半径（环的外边缘）  
    teeth_count: int = 186,         # 齿的数量
    teeth_angle_fraction: float = 0.2,      # 每个齿的角度宽度（度）
    teeth_length:float =22,
    layer: tuple = (4, 0)          # 金属层
):
    """
    创建带有齿状结构的圆环电极
    
    Args:
        center_x, center_y: 圆心坐标
        inner_radius: 内圆半径（齿的内端半径）
        outer_radius: 外圆半径（环的外边缘半径）
        teeth_count: 齿的数量
        teeth_angle: 每个齿的角度宽度（度）
        layer: 层
        
    注意：齿从inner_radius延伸，环从齿的外端到outer_radius
    """
    c = gf.Component()
    teeth_angle=teeth_angle_fraction*360/teeth_count
    # 计算齿的外端半径（环的内边缘）
    # 假设齿占总半径差的70%，环占30%

    
    # 1. 创建基础圆环（从齿的外端到外圆）
    ring_width = outer_radius - inner_radius
    ring = gf.components.ring(
        radius=inner_radius + ring_width/2,  # 环的中心半径
        width=ring_width,
        layer=layer
    )
    ring_ref = c << ring
    ring_ref.dmove([center_x, center_y])
    
    # 2. 计算齿的角度分布
    teeth_angles = np.linspace(0, 2*np.pi, teeth_count, endpoint=False)
    teeth_angle_rad = np.deg2rad(teeth_angle)
    
    # 3. 为每个齿创建扇形
    for angle in teeth_angles:
        # 创建扇形齿（从圆心扫出）
        # 扇形的起始和结束角度
        start_angle = angle - teeth_angle_rad/2
        end_angle = angle + teeth_angle_rad/2
        
        # 创建扇形的点
        n_points = 20  # 扇形弧的点数
        arc_angles = np.linspace(start_angle, end_angle, n_points)
        
        # 构建扇形的所有点
        points = []
        
        # 内弧点（从起始角到结束角）
        for a in arc_angles:
            x = center_x + inner_radius * np.cos(a)
            y = center_y + inner_radius * np.sin(a)
            points.append([x, y])
            
        # 外弧点（从结束角到起始角，反向）
        for a in reversed(arc_angles):
            x = center_x + (inner_radius-teeth_length) * np.cos(a)
            y = center_y + (inner_radius-teeth_length) * np.sin(a)
            points.append([x, y])
        
        # 创建齿的多边形
        c.add_polygon(points, layer=layer)
    
    # 4. 在圆环正上方添加圆形pad
    pad_radius = 50
    pad_distance = 10
    pad_center_y = center_y + outer_radius + pad_distance+pad_radius
    
    # 创建圆形pad
    pad = gf.components.circle(radius=pad_radius, layer=layer)
    pad_ref = c << pad
    pad_ref.dmove([center_x, pad_center_y])
    
    # 5. 用多边形连接圆环和pad
    # 圆环上的连接点（正上方几个点）
    ring_points = []
    for i in range(-1, 2):  # 3个点：左、中、右
        angle = np.pi/2 + i * 0.1/outer_radius*pad_radius  # 90度附近的3个点
        x = center_x + outer_radius * np.cos(angle)
        y = center_y + outer_radius * np.sin(angle)
        ring_points.append([x, y])
    
    # pad底部的连接点（对应的几个点）
    pad_points = []
    for i in range(-1, 2):  # 3个点
        angle = -np.pi/2 - i * 0.1  # pad底部对应点
        x = center_x + pad_radius * np.cos(angle)
        y = pad_center_y + pad_radius * np.sin(angle)
        pad_points.append([x, y])
    
    # 创建连接多边形
    connection_points = ring_points + list(reversed(pad_points))
    c.add_polygon(connection_points, layer=layer)
    
    c.flatten()
    return c




@gf.cell
def thermal_heater_ring(
    # Ring parameters
    ring_radius: float = 100,           # Ring radius (μm)
    # Heater parameter
    heater_angle: float = 140,          # Heater arc angle (degrees)
    heater_width: float = 5,            # Heater line width (μm)
    heater_layer: tuple = (49, 0),      # Heater layer
):
    c = gf.Component()
    heater_rad = np.deg2rad(heater_angle)
    # 定义加热器横截面
    cross_section_heater = gf.cross_section.cross_section(
        width=heater_width, layer=heater_layer,
        port_names=("e1", "e3"),  # 良好的编程习惯，为电气端口命名
        port_types=("electrical", "electrical"),  #
    )
    #这是布线的
    cross_section_heater_route = gf.cross_section.cross_section(
        width=heater_width, layer=heater_layer,
        port_names=("e1", "e3"),  # 良好的编程习惯，为电气端口命名
        port_types=("electrical", "electrical")  #
    )
   # cross_check=gf.get_cross_section("metal_routing", width=heater_width, layer=heater_layer)
    # 使用gdsfactory的 arc 函数创建热极圆弧
    heater_waveguide =gf.components.bend_circular(radius=ring_radius, angle=heater_angle, 
                                                       cross_section=cross_section_heater, 
                                                       allow_min_radius_violation=False)
    heater_ref = c << heater_waveguide
    #x=heater_ref.xmin()
    c.rotate(-heater_angle/2)
    points_left=[(heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)),
            (heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+10),
            (-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+10),
           (-heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2))]
    c.add_polygon(points_left, layer=heater_layer)
    points_right=[(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)),
            (2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+10),
           (2*ring_radius*np.sin(heater_rad/2)-heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+10),
           (2*ring_radius*np.sin(heater_rad/2)-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2))]
    c.add_polygon(points_right, layer=heater_layer)
    c2=gf.Component()
    c2.add_port(
        name="e1",
        center=(-heater_width/2*np.sin(heater_rad/2),heater_width/2*np.cos(heater_rad/2)+5),
        width=5,
        orientation=180,
        layer=heater_layer,
        port_type="electrical"
    )
    c2.add_port(
        name="e2",
        center=(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2),-heater_width/2*np.cos(heater_rad/2)+5),
        width=5,
        orientation=0,
        layer=heater_layer,
        port_type="electrical"
    )
    rinner = 2000  # 	The circle radius of inner corners (in database units).
    router = 2000  # 	The circle radius of outer corners (in database units).
    n=2000
    c.flatten()
    for layer, polygons in c.get_polygons().items():
        for p in polygons:
            p_round = p.round_corners(rinner, router, n)
            c2.add_polygon(p_round, layer=layer)
    #pad的数据
    pad_ref1=c2<<gf.components.pad(size=(10, 10), layer=heater_layer, port_inclusion=0, port_orientation=0, port_orientations=(180, 90, 0, -90), port_type='pad')
    pad_ref1.center=(-heater_width/2*np.sin(heater_rad/2)-20,heater_width/2*np.cos(heater_rad/2)+5)
    
    pad_ref2=c2<<gf.components.pad(size=(10, 10), layer=heater_layer, port_inclusion=0, port_orientation=0, port_orientations=(180, 90, 0, -90), port_type='pad')
    pad_ref2.center=(2*ring_radius*np.sin(heater_rad/2)+heater_width/2*np.sin(heater_rad/2)+20,-heater_width/2*np.cos(heater_rad/2)+5)
    
    
    route1 = gf.routing.route_single(
        c2,
        pad_ref1.ports["e3"],
        c2.ports["e1"],
        bend="wire_corner45",
        port_type="electrical",
        cross_section=cross_section_heater_route,
        allow_width_mismatch=True,
    )
    route2 = gf.routing.route_single(
        c2,
        pad_ref2.ports["e1"],
        c2.ports["e2"],
        bend="wire_corner45",
        port_type="electrical",
        cross_section=cross_section_heater_route,
        allow_width_mismatch=True,
        auto_taper=True,
    )
    c3=gf.Component()
    c3<<c2
    c3.add_port("e1",port=pad_ref1.ports["e2"])
    c3.add_port("e2",port=pad_ref2.ports["e2"])
    c3.add_port("e3",port=pad_ref1.ports["e4"])
    c3.add_port("e4",port=pad_ref2.ports["e4"])
    #c3.draw_ports()
    c3.flatten()
    return c3

# # 创建和测试带热极的环形器件
# print("=== 创建带热极的环形谐振器 ===")
# ring_heater = thermal_heater_ring()
# ring_heater.draw_ports()
# ring_heater.plot()
# ring_heater.show()

# 演示不同参数的电极
# dense_electrode = create_ring_electrode_with_teeth()
# dense_electrode.plot()
# dense_electrode.show()
@gf.cell
def ring_with_electrode(ring_width:float=1.5,
                        waveguide_width:float=0.6,
                        gap:float=0.6,
                        coupling_angle_coverage:float=10,
                        ring_radius:float=100-1.5/2
                        ):
    section_inner = gf.cross_section.cross_section(
        width=ring_width,
        offset=0,
        layer=(1, 0)
    )
    section_outer = gf.cross_section.cross_section(
        width=waveguide_width,
        offset=0,
        layer=(1, 0)
    )

    # 创建基础环形耦合器
    coupler = gf.components.ring_single_bend_coupler(
        radius=ring_radius, gap=gap, 
        coupling_angle_coverage=coupling_angle_coverage, bend='bend_circular', 
        bend_output='bend_euler_all_angle', 
        length_x=0, length_y=0, 
        cross_section_inner=section_inner, 
        cross_section_outer=section_outer)

    # 创建新的组件
    c = gf.Component()

    # 添加耦合器
    coupler_ref = c << coupler
    electrode_ref = c << create_ring_electrode_with_teeth()
    coupler_ref.dcenter=(0,0)
    y=coupler_ref.ymin
    y1=electrode_ref.ymin
    x1=electrode_ref.xmin
    x2=electrode_ref.xmax
    electrode_ref.ymin=y+ring_width/2+ring_radius-(x2-x1)/2
    # # 添加两个直波导
    s1 = c << gf.get_component("straight", length=100, cross_section=section_outer)
    s2 = c << gf.get_component("straight", length=100, cross_section=section_outer)

    # # 连接直波导到耦合器的端口
    s1.connect("o1", coupler_ref.ports["o1"])
    s2.connect("o1", coupler_ref.ports["o2"])


    # 添加端口（新的端口位置）
    c.add_port("o1", port=s1.ports["o2"])
    # c.add_port("o2", port=coupler_ref.ports["o2"])
    # c.add_port("o3", port=coupler_ref.ports["o1"])
    c.add_port("o2", port=s2.ports["o2"])
    return c


@gf.cell
def ring_with_electrode_with_heater(ring_width:float=1.5,
                        waveguide_width:float=0.6,
                        gap:float=0.6,
                        coupling_angle_coverage:float=10,
                        ring_radius:float=100-1.5/2,
                        heater_width:float=3,
                        ):
    section_inner = gf.cross_section.cross_section(
        width=ring_width,
        offset=0,
        layer=(1, 0)
    )
    section_outer = gf.cross_section.cross_section(
        width=waveguide_width,
        offset=0,
        layer=(1, 0)
    )

    # 创建基础环形耦合器
    coupler = gf.components.ring_single_bend_coupler(
        radius=ring_radius, gap=gap, 
        coupling_angle_coverage=coupling_angle_coverage, bend='bend_circular', 
        bend_output='bend_euler_all_angle', 
        length_x=0, length_y=0, 
        cross_section_inner=section_inner, 
        cross_section_outer=section_outer)

    # 创建新的组件
    c = gf.Component()

    # 添加耦合器
    coupler_ref = c << coupler
    electrode_ref = c << create_ring_electrode_with_teeth()
    heater_ref=c<<thermal_heater_ring(ring_radius=ring_radius,
                                      heater_width=heater_width,
                                      heater_angle=230,)
    coupler_ref.dcenter=(0,0)
    y=coupler_ref.ymin
    y1=electrode_ref.ymin
    x1=electrode_ref.xmin
    x2=electrode_ref.xmax
    electrode_ref.ymin=y+ring_width/2+ring_radius-(x2-x1)/2
    heater_ref.dcenter=(0,0)
    heater_ref.ymin=y+ring_width/2-heater_width/2
    # # 添加两个直波导
    s1 = c << gf.get_component("straight", length=100, cross_section=section_outer)
    s2 = c << gf.get_component("straight", length=100, cross_section=section_outer)

    # # 连接直波导到耦合器的端口
    s1.connect("o1", coupler_ref.ports["o1"])
    s2.connect("o1", coupler_ref.ports["o2"])


    # 添加端口（新的端口位置）
    c.add_port("o1", port=s1.ports["o2"])
    # c.add_port("o2", port=coupler_ref.ports["o2"])
    # c.add_port("o3", port=coupler_ref.ports["o1"])
    c.add_port("o2", port=s2.ports["o2"])
    c.add_port("e1",port=heater_ref.ports["e1"])
    c.add_port("e2",port=heater_ref.ports["e2"])
    c.add_port("e3",port=heater_ref.ports["e3"])
    c.add_port("e4",port=heater_ref.ports["e4"])
    return c
#c = gf.components.extend_ports(component=c, length=50, port_type='optical', centered=False, allow_width_mismatch=False, auto_taper=True).copy()
c=ring_with_electrode_with_heater()
c.draw_ports()
c.plot()
c.show()
c.pprint_ports()