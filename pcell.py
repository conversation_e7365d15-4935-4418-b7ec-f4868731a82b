@gf.cell
def gvc_bend_optimal(
    final_angle: float = 45.0,
    final_curvature: float = -0.1,
    peak_curvature: float = 0.2,
    cross_section: gf.typings.CrossSectionSpec = "strip",
    npoints: int = 720,
) -> gf.Component:
    """
    创建一个理论最优的通用可变曲率弯曲 (C2连续)。

    该弯曲的曲率 k(s) 及其一阶导数 dk/ds 全程连续，
    从而最大限度地减少过渡损耗。端口角度被强制设为精确值，
    以避免 "off-grid" 错误。

    Args:
        final_angle: 最终的目标角度 (degrees)。
        final_curvature: 最终的目标曲率 (1/μm)。正=逆时针, 负=顺时针。
        peak_curvature: 路径中的峰值曲率 (1/μm)。这是一个正值，
                        用作损耗/尺寸的控制参数。值越小，弯曲越平缓，
                        损耗越低，但尺寸越大。
        cross_section: 波导的横截面。
        npoints: 离散化点数。
    """
    # --- 输入参数验证 ---
    k_final = final_curvature
    k_peak = peak_curvature
    
    if k_peak <= 0:
        raise ValueError("peak_curvature 必须为正数。")
    if np.abs(k_final) >= k_peak:
        raise ValueError(
            f"peak_curvature ({k_peak}) 必须大于等于最终曲率的绝对值 "
            f"abs(final_curvature) ({np.abs(k_final)})."
        )

    # --- 核心逻辑: 根据输入参数反解路径长度 L1 和 L2 ---
    theta_final_rad = np.deg2rad(final_angle)
    
    denominator = (k_peak / 2) + (k_peak + k_final) / 2 * (k_peak - k_final) / k_peak
    if np.isclose(denominator, 0):
        denominator = 1e-9
    L1 = theta_final_rad / denominator

    L2 = L1 * (k_peak - k_final) / k_peak
    
    if L1 < 0 or L2 < 0:
        raise ValueError(
            "计算出的路径长度为负，无法创建弯曲。 "
            "请尝试增大 'peak_curvature' 或减小 'final_angle'。"
        )

    # --- 构建路径 ---
    s_total = np.linspace(0, L1 + L2, npoints)
    k = np.zeros_like(s_total)

    for i, s in enumerate(s_total):
        if s <= L1:
            k[i] = (k_peak / 2) * (1 - np.cos(np.pi * s / L1))
        else:
            s_prime = s - L1
            k[i] = (k_peak + k_final) / 2 + (k_peak - k_final) / 2 * np.cos(np.pi * s_prime / L2)

    theta_cum = np.zeros_like(s_total)
    for i in range(1, npoints):
        theta_cum[i] = theta_cum[i-1] + (k[i] + k[i-1]) * (s_total[i] - s_total[i-1]) / 2

    x = np.zeros_like(s_total)
    y = np.zeros_like(s_total)
    for i in range(1, npoints):
        ds = s_total[i] - s_total[i-1]
        avg_angle = (theta_cum[i] + theta_cum[i-1]) / 2
        x[i] = x[i-1] + np.cos(avg_angle) * ds
        y[i] = y[i-1] + np.sin(avg_angle) * ds
        
    points = np.column_stack((x, y))

    # --- 创建 gdsfactory 组件 (最关键的修正) ---
    c = gf.Component()
    
    # 1. 创建一个 Path 对象并赋予其几何点
    path = gf.Path()
    path.points = points
    
    # 2. 强制设定完美的起始和结束角度，覆盖数值误差
    #    假设输入总是水平向右
    path.start_angle = 0.0
    path.end_angle = final_angle
    
    # 3. 使用这个修正了角度信息的 Path 对象来创建组件
    bend = c << gf.path.extrude(path, cross_section=cross_section)
    c.add_ports(bend.ports)
    
    return c